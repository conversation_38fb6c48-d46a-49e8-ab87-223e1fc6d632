<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('players', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('position', ['GK', 'DF', 'MF', 'FW']);
            $table->integer('shirt_number')->unique();
            $table->string('photo')->nullable();
            $table->string('nationality');
            $table->text('bio')->nullable();
            $table->json('stats')->nullable(); // goals, assists, appearances, etc.
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('players');
    }
};
