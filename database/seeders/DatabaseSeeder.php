<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\News;
use App\Models\Player;
use App\Models\Fixture;
use App\Models\Merchandise;
use App\Models\Sponsor;
use App\Models\MediaGallery;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
        ]);

        // Create editor user
        $editor = User::create([
            'name' => 'Editor User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'editor',
        ]);

        // Create fan user
        User::create([
            'name' => 'Fan User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'fan',
        ]);

        // Create players
        $players = [
            ['name' => '<PERSON>', 'position' => 'GK', 'shirt_number' => 1, 'nationality' => 'Tanzania', 'bio' => 'Experienced goalkeeper with excellent reflexes.'],
            ['name' => 'Peter Kimaro', 'position' => 'DF', 'shirt_number' => 2, 'nationality' => 'Tanzania', 'bio' => 'Solid defender with great leadership skills.'],
            ['name' => 'David Mollel', 'position' => 'DF', 'shirt_number' => 3, 'nationality' => 'Tanzania', 'bio' => 'Fast left-back with attacking prowess.'],
            ['name' => 'Emmanuel Lyimo', 'position' => 'DF', 'shirt_number' => 4, 'nationality' => 'Tanzania', 'bio' => 'Central defender known for his aerial ability.'],
            ['name' => 'Francis Mushi', 'position' => 'DF', 'shirt_number' => 5, 'nationality' => 'Tanzania', 'bio' => 'Captain and center-back with years of experience.'],
            ['name' => 'Joseph Massawe', 'position' => 'MF', 'shirt_number' => 6, 'nationality' => 'Tanzania', 'bio' => 'Defensive midfielder with excellent passing range.'],
            ['name' => 'Michael Swai', 'position' => 'MF', 'shirt_number' => 7, 'nationality' => 'Tanzania', 'bio' => 'Creative midfielder with pace and skill.'],
            ['name' => 'Robert Minja', 'position' => 'MF', 'shirt_number' => 8, 'nationality' => 'Tanzania', 'bio' => 'Box-to-box midfielder with great work rate.'],
            ['name' => 'Daniel Kiwia', 'position' => 'FW', 'shirt_number' => 9, 'nationality' => 'Tanzania', 'bio' => 'Clinical striker and top scorer.'],
            ['name' => 'Samuel Mbwilo', 'position' => 'FW', 'shirt_number' => 10, 'nationality' => 'Tanzania', 'bio' => 'Playmaker and team\'s creative force.'],
            ['name' => 'George Mwakasege', 'position' => 'FW', 'shirt_number' => 11, 'nationality' => 'Tanzania', 'bio' => 'Winger with pace and crossing ability.'],
        ];

        foreach ($players as $playerData) {
            Player::create($playerData);
        }

        // Create fixtures
        $fixtures = [
            [
                'opponent' => 'Simba SC',
                'competition' => 'Premier League',
                'match_date' => now()->addDays(7),
                'venue' => 'Sheikh Amri Abeid Memorial Stadium',
                'home_or_away' => 'home',
                'status' => 'upcoming'
            ],
            [
                'opponent' => 'Young Africans',
                'competition' => 'Premier League',
                'match_date' => now()->addDays(14),
                'venue' => 'Benjamin Mkapa Stadium',
                'home_or_away' => 'away',
                'status' => 'upcoming'
            ],
            [
                'opponent' => 'Azam FC',
                'competition' => 'Premier League',
                'match_date' => now()->subDays(7),
                'venue' => 'Sheikh Amri Abeid Memorial Stadium',
                'home_or_away' => 'home',
                'status' => 'finished',
                'score_mbuni' => 2,
                'score_opponent' => 1
            ],
            [
                'opponent' => 'Coastal Union',
                'competition' => 'Premier League',
                'match_date' => now()->subDays(14),
                'venue' => 'Mkwakwani Stadium',
                'home_or_away' => 'away',
                'status' => 'finished',
                'score_mbuni' => 1,
                'score_opponent' => 1
            ],
        ];

        foreach ($fixtures as $fixtureData) {
            Fixture::create($fixtureData);
        }

        // Create news articles
        $newsArticles = [
            [
                'title' => 'Mbuni FC Defeats Azam FC 2-1 in Thrilling Match',
                'slug' => 'mbuni-fc-defeats-azam-fc-2-1-thrilling-match',
                'body' => '<p>In a spectacular display of football, Mbuni FC secured a crucial 2-1 victory against Azam FC at the Sheikh Amri Abeid Memorial Stadium. The match was filled with excitement from start to finish.</p><p>Daniel Kiwia opened the scoring in the 23rd minute with a brilliant strike from outside the box. Samuel Mbwilo doubled the lead just before halftime with a well-placed header from a corner kick.</p><p>Azam FC pulled one back in the second half, but Mbuni FC\'s defense held firm to secure all three points.</p>',
                'category' => 'match_report',
                'status' => 'published',
                'author_id' => $editor->id,
                'published_at' => now()->subDays(1)
            ],
            [
                'title' => 'New Signing: Michael Swai Joins Mbuni FC',
                'slug' => 'new-signing-michael-swai-joins-mbuni-fc',
                'body' => '<p>We are delighted to announce the signing of talented midfielder Michael Swai from Mbeya City FC. The 24-year-old brings pace, skill, and creativity to our midfield.</p><p>"I\'m excited to join Mbuni FC and contribute to the team\'s success," said Swai. "The club has great ambitions and I want to be part of that journey."</p>',
                'category' => 'transfer',
                'status' => 'published',
                'author_id' => $admin->id,
                'published_at' => now()->subDays(3)
            ],
            [
                'title' => 'Upcoming Match Preview: Mbuni FC vs Simba SC',
                'slug' => 'upcoming-match-preview-mbuni-fc-vs-simba-sc',
                'body' => '<p>This Sunday, Mbuni FC will face the mighty Simba SC at home in what promises to be an exciting encounter. Both teams are in good form and will be looking to secure three points.</p><p>Coach Francis Baraza has been working hard with the team in preparation for this crucial match. "We respect Simba SC, but we believe in our abilities and will give our best," he said.</p>',
                'category' => 'club_news',
                'status' => 'published',
                'author_id' => $editor->id,
                'published_at' => now()->subHours(6)
            ]
        ];

        foreach ($newsArticles as $articleData) {
            News::create($articleData);
        }

        // Create merchandise
        $merchandise = [
            [
                'name' => 'Mbuni FC Home Jersey 2024',
                'description' => 'Official home jersey for the 2024 season. Made with high-quality materials for comfort and durability.',
                'price' => 45000,
                'stock_quantity' => 50,
                'category' => 'jersey'
            ],
            [
                'name' => 'Mbuni FC Away Jersey 2024',
                'description' => 'Official away jersey for the 2024 season. Stylish design with club colors.',
                'price' => 45000,
                'stock_quantity' => 30,
                'category' => 'jersey'
            ],
            [
                'name' => 'Mbuni FC Scarf',
                'description' => 'Show your support with this official Mbuni FC scarf. Perfect for match days.',
                'price' => 15000,
                'stock_quantity' => 100,
                'category' => 'accessories'
            ],
            [
                'name' => 'Mbuni FC Cap',
                'description' => 'Official club cap with embroidered logo. One size fits all.',
                'price' => 20000,
                'stock_quantity' => 75,
                'category' => 'accessories'
            ]
        ];

        foreach ($merchandise as $item) {
            Merchandise::create($item);
        }

        // Create sponsors
        $sponsors = [
            ['name' => 'Vodacom Tanzania', 'website_url' => 'https://vodacom.co.tz'],
            ['name' => 'NBC Bank', 'website_url' => 'https://nbctz.com'],
            ['name' => 'Azania Bank', 'website_url' => 'https://azaniabank.co.tz'],
            ['name' => 'Precision Air', 'website_url' => 'https://precisionairtz.com']
        ];

        foreach ($sponsors as $sponsor) {
            Sponsor::create($sponsor);
        }

        // Create media gallery items
        $mediaItems = [
            [
                'title' => 'Team Training Session',
                'file_type' => 'image',
                'file_url' => 'media/training-session.jpg',
                'caption' => 'Players during intensive training session',
                'uploaded_by' => $admin->id
            ],
            [
                'title' => 'Match Highlights vs Azam FC',
                'file_type' => 'video',
                'file_url' => 'https://youtube.com/watch?v=example',
                'caption' => 'Best moments from our 2-1 victory',
                'uploaded_by' => $editor->id
            ]
        ];

        foreach ($mediaItems as $item) {
            MediaGallery::create($item);
        }
    }
}
