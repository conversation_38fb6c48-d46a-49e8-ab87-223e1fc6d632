# Mbuni FC API Documentation

## Base URL
```
http://your-domain.com/api/v1
```

## Authentication

The API uses Laravel Sanctum for authentication. To access protected endpoints, include the Bearer token in the Authorization header:

```
Authorization: Bearer your-api-token
```

### Authentication Endpoints

#### Login
```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password"
}
```

#### Register
```http
POST /api/v1/auth/register
Content-Type: application/json

{
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "password": "password",
    "password_confirmation": "password"
}
```

#### Logout (Protected)
```http
POST /api/v1/auth/logout
Authorization: Bearer your-api-token
```

#### Get User Info (Protected)
```http
GET /api/v1/auth/user
Authorization: Bearer your-api-token
```

## Public Endpoints

### News

#### Get All News
```http
GET /api/v1/news
```

Query Parameters:
- `category` - Filter by category (match_report, club_news, transfer, interview)
- `search` - Search in title and body
- `per_page` - Items per page (max 50, default 15)
- `page` - Page number

#### Get Single News Article
```http
GET /api/v1/news/{id}
```

#### Get News by Category
```http
GET /api/v1/news/category/{category}
```

### Fixtures

#### Get All Fixtures
```http
GET /api/v1/fixtures
```

Query Parameters:
- `status` - Filter by status (upcoming, finished, cancelled)
- `from_date` - Filter from date (YYYY-MM-DD)
- `to_date` - Filter to date (YYYY-MM-DD)
- `venue_type` - Filter by venue (home, away)
- `per_page` - Items per page (max 50, default 15)

#### Get Single Fixture
```http
GET /api/v1/fixtures/{id}
```

#### Get Upcoming Fixtures
```http
GET /api/v1/fixtures/upcoming
```

#### Get Results
```http
GET /api/v1/fixtures/results
```

### Players

#### Get All Players
```http
GET /api/v1/players
```

Query Parameters:
- `position` - Filter by position (GK, DF, MF, FW)
- `search` - Search in name and nationality
- `per_page` - Items per page (max 50, default 25)

#### Get Single Player
```http
GET /api/v1/players/{id}
```

#### Get Players by Position
```http
GET /api/v1/players/position/{position}
```

### Store (Merchandise)

#### Get All Merchandise
```http
GET /api/v1/store
```

Query Parameters:
- `category` - Filter by category
- `in_stock` - Filter available items (true/false)
- `search` - Search in name and description
- `min_price` - Minimum price filter
- `max_price` - Maximum price filter
- `sort_by` - Sort by (name, price, created_at)
- `sort_order` - Sort order (asc, desc)
- `per_page` - Items per page (max 50, default 20)

#### Get Single Merchandise
```http
GET /api/v1/store/{id}
```

#### Get Merchandise by Category
```http
GET /api/v1/store/category/{category}
```

### Media Gallery

#### Get All Media
```http
GET /api/v1/gallery
```

Query Parameters:
- `type` - Filter by type (image, video)
- `search` - Search in title and caption
- `per_page` - Items per page (max 50, default 20)

#### Get Single Media
```http
GET /api/v1/gallery/{id}
```

#### Get Images Only
```http
GET /api/v1/gallery/images
```

#### Get Videos Only
```http
GET /api/v1/gallery/videos
```

## Response Format

All API responses follow this format:

### Success Response
```json
{
    "data": [...],
    "meta": {
        "current_page": 1,
        "last_page": 5,
        "per_page": 15,
        "total": 75,
        "from": 1,
        "to": 15
    },
    "links": {
        "first": "http://example.com/api/v1/news?page=1",
        "last": "http://example.com/api/v1/news?page=5",
        "prev": null,
        "next": "http://example.com/api/v1/news?page=2"
    }
}
```

### Error Response
```json
{
    "message": "Error description",
    "status": 400
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse. Default limits:
- 60 requests per minute for authenticated users
- 30 requests per minute for unauthenticated users

## CORS

Cross-Origin Resource Sharing (CORS) is enabled for all origins in development. Configure appropriately for production.

## Example Usage

### JavaScript/Fetch
```javascript
// Get news
fetch('http://your-domain.com/api/v1/news')
    .then(response => response.json())
    .then(data => console.log(data));

// Login
fetch('http://your-domain.com/api/v1/auth/login', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password'
    })
})
.then(response => response.json())
.then(data => {
    localStorage.setItem('token', data.token);
});
```

### cURL
```bash
# Get upcoming fixtures
curl -X GET "http://your-domain.com/api/v1/fixtures/upcoming" \
     -H "Accept: application/json"

# Login
curl -X POST "http://your-domain.com/api/v1/auth/login" \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"password"}'
```
