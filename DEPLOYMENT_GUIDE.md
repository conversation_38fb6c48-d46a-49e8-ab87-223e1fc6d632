# Mbuni FC - Production Deployment Guide

## 🚀 Production Deployment

### Server Requirements
- **PHP**: 8.2 or higher
- **Web Server**: Apache/Nginx
- **Database**: MySQL 8.0+ or PostgreSQL 13+
- **Memory**: Minimum 512MB RAM
- **Storage**: 2GB+ available space
- **SSL Certificate**: Required for production

### Environment Setup

#### 1. Clone and Install
```bash
# Clone repository
git clone <repository-url> /var/www/mbuni_fc
cd /var/www/mbuni_fc

# Install dependencies
composer install --optimize-autoloader --no-dev
npm install && npm run build

# Set permissions
sudo chown -R www-data:www-data /var/www/mbuni_fc
sudo chmod -R 755 /var/www/mbuni_fc
sudo chmod -R 775 /var/www/mbuni_fc/storage
sudo chmod -R 775 /var/www/mbuni_fc/bootstrap/cache
```

#### 2. Environment Configuration
```bash
# Copy and configure environment
cp .env.example .env
php artisan key:generate
```

#### 3. Production .env Settings
```env
APP_NAME="Mbuni FC"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://mbunifc.co.tz

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=mbuni_fc
DB_USERNAME=mbuni_user
DB_PASSWORD=secure_password

CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Mbuni FC"

SANCTUM_STATEFUL_DOMAINS=mbunifc.co.tz,www.mbunifc.co.tz
SESSION_DOMAIN=.mbunifc.co.tz
```

#### 4. Database Setup
```bash
# Create database
mysql -u root -p
CREATE DATABASE mbuni_fc CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'mbuni_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON mbuni_fc.* TO 'mbuni_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# Run migrations
php artisan migrate --force
php artisan db:seed --force
```

#### 5. Storage and Cache
```bash
# Create storage link
php artisan storage:link

# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache
```

### Web Server Configuration

#### Nginx Configuration
```nginx
server {
    listen 80;
    listen [::]:80;
    server_name mbunifc.co.tz www.mbunifc.co.tz;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name mbunifc.co.tz www.mbunifc.co.tz;
    root /var/www/mbuni_fc/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    # SSL Configuration
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Security headers
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;";
}
```

#### Apache Configuration (.htaccess)
```apache
<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>
```

### Redis Setup
```bash
# Install Redis
sudo apt update
sudo apt install redis-server

# Configure Redis
sudo systemctl enable redis-server
sudo systemctl start redis-server

# Test Redis
redis-cli ping
```

### Queue Worker Setup
```bash
# Create systemd service
sudo nano /etc/systemd/system/mbuni-worker.service
```

```ini
[Unit]
Description=Mbuni FC Queue Worker
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/var/www/mbuni_fc
ExecStart=/usr/bin/php /var/www/mbuni_fc/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start worker
sudo systemctl enable mbuni-worker
sudo systemctl start mbuni-worker
```

### Monitoring & Maintenance

#### Log Rotation
```bash
# Create logrotate configuration
sudo nano /etc/logrotate.d/mbuni-fc
```

```
/var/www/mbuni_fc/storage/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload php8.2-fpm
    endscript
}
```

#### Backup Script
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/mbuni_fc"
APP_DIR="/var/www/mbuni_fc"

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u mbuni_user -p mbuni_fc > $BACKUP_DIR/database_$DATE.sql

# Files backup
tar -czf $BACKUP_DIR/files_$DATE.tar.gz $APP_DIR/storage/app/public

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

### Security Checklist

- [ ] SSL certificate installed and configured
- [ ] Database credentials secured
- [ ] File permissions set correctly
- [ ] Debug mode disabled in production
- [ ] Security headers configured
- [ ] Regular backups scheduled
- [ ] Monitoring tools configured
- [ ] Firewall rules applied
- [ ] Regular security updates scheduled

### Performance Optimization

#### PHP-FPM Configuration
```ini
; /etc/php/8.2/fpm/pool.d/www.conf
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500
```

#### MySQL Optimization
```sql
-- Add to /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
query_cache_size = 32M
max_connections = 100
```

### Troubleshooting

#### Common Issues
1. **Permission Errors**: Check file ownership and permissions
2. **Database Connection**: Verify credentials and server status
3. **Cache Issues**: Clear all caches with `php artisan optimize:clear`
4. **Queue Not Processing**: Check worker service status
5. **SSL Issues**: Verify certificate installation and configuration

#### Useful Commands
```bash
# Clear all caches
php artisan optimize:clear

# Check application status
php artisan about

# Monitor logs
tail -f storage/logs/laravel.log

# Check queue status
php artisan queue:monitor

# Restart services
sudo systemctl restart nginx php8.2-fpm redis-server
```

---

**Deployment Status**: Ready for Production
**Security Level**: High
**Performance**: Optimized
