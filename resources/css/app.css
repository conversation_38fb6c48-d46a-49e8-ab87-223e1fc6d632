/* Import Google Fonts for better typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Mbuni FC Modern Styles */
@layer base {
    html {
        scroll-behavior: smooth;
    }

    body {
        font-family: 'Inter', sans-serif;
    }
}

@layer components {
    /* Modern Button Styles */
    .btn-primary {
        @apply bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-105 hover:shadow-lg;
    }

    .btn-secondary {
        @apply bg-gradient-to-r from-gray-200 to-gray-300 hover:from-gray-300 hover:to-gray-400 text-gray-800 font-semibold py-3 px-6 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-105 hover:shadow-lg;
    }

    .btn-outline {
        @apply border-2 border-primary-600 text-primary-600 hover:bg-gradient-to-r hover:from-primary-600 hover:to-primary-700 hover:text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-105 hover:shadow-lg;
    }

    /* Glassmorphism Effects */
    .glass {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    }

    .glass-dark {
        background: rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
    }

    .glass-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.4);
        border-radius: 16px;
    }

    /* Neumorphism Effects */
    .neomorphism {
        background: #f0f0f0;
        border-radius: 20px;
        box-shadow: 20px 20px 60px #bebebe, -20px -20px 60px #ffffff;
        transition: all 0.3s ease;
    }

    .neomorphism:hover {
        box-shadow: inset 20px 20px 60px #bebebe, inset -20px -20px 60px #ffffff;
    }

    .neomorphism-dark {
        background: #2d2d2d;
        border-radius: 20px;
        box-shadow: 20px 20px 60px #1a1a1a, -20px -20px 60px #404040;
        transition: all 0.3s ease;
    }

    .neomorphism-dark:hover {
        box-shadow: inset 20px 20px 60px #1a1a1a, inset -20px -20px 60px #404040;
    }

    /* Enhanced Card Styles */
    .card {
        @apply bg-white rounded-2xl shadow-lg overflow-hidden transition-all duration-300 ease-in-out hover:shadow-2xl hover:scale-105;
    }

    .card-modern {
        @apply bg-white rounded-2xl shadow-lg overflow-hidden transition-all duration-500 ease-in-out hover:shadow-2xl;
        transform: translateY(0);
    }

    .card-modern:hover {
        transform: translateY(-8px);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    .card-glass {
        @apply glass rounded-2xl overflow-hidden transition-all duration-300 ease-in-out hover:shadow-2xl;
    }

    .card-header {
        @apply px-6 py-4 border-b border-gray-200;
    }

    .card-body {
        @apply px-6 py-4;
    }

    /* Enhanced Badge Styles */
    .badge {
        @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold transition-all duration-200;
    }

    .badge-primary {
        @apply bg-gradient-to-r from-primary-100 to-primary-200 text-primary-800 hover:from-primary-200 hover:to-primary-300;
    }

    .badge-success {
        @apply bg-gradient-to-r from-green-100 to-green-200 text-green-800 hover:from-green-200 hover:to-green-300;
    }

    .badge-warning {
        @apply bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-800 hover:from-yellow-200 hover:to-yellow-300;
    }

    .badge-danger {
        @apply bg-gradient-to-r from-red-100 to-red-200 text-red-800 hover:from-red-200 hover:to-red-300;
    }

    /* Modern Gradient Backgrounds */
    .hero-gradient {
        background: linear-gradient(135deg, #dc2626 0%, #991b1b 50%, #7f1d1d 100%);
        position: relative;
    }

    .hero-gradient::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.1) 100%);
        animation: shimmer 3s ease-in-out infinite;
    }

    .hero-gradient-animated {
        background: linear-gradient(-45deg, #dc2626, #991b1b, #7f1d1d, #dc2626);
        background-size: 400% 400%;
        animation: gradientShift 8s ease infinite;
    }

    .text-gradient {
        background: linear-gradient(135deg, #dc2626 0%, #991b1b 50%, #fbbf24 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: gradientText 3s ease-in-out infinite alternate;
    }

    /* Hover Effects */
    .hover-lift {
        transition: all 0.3s ease;
    }

    .hover-lift:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .hover-glow {
        transition: all 0.3s ease;
    }

    .hover-glow:hover {
        box-shadow: 0 0 20px rgba(220, 38, 38, 0.5);
    }

    .hover-scale {
        transition: transform 0.3s ease;
    }

    .hover-scale:hover {
        transform: scale(1.05);
    }

    /* Image Effects */
    .image-zoom {
        overflow: hidden;
        border-radius: 12px;
    }

    .image-zoom img {
        transition: transform 0.5s ease;
    }

    .image-zoom:hover img {
        transform: scale(1.1);
    }

    /* Loading Skeleton */
    .skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    /* Ripple Effect */
    .ripple {
        position: relative;
        overflow: hidden;
    }

    .ripple::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.5);
        transform: translate(-50%, -50%);
        transition: width 0.6s, height 0.6s;
    }

    .ripple:active::before {
        width: 300px;
        height: 300px;
    }

    /* Form Enhancements */
    .form-input-glow {
        @apply border-2 border-gray-300 rounded-xl px-4 py-3 transition-all duration-300 focus:border-primary-500 focus:ring-4 focus:ring-primary-200 focus:outline-none;
    }

    .form-input-glow:focus {
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1), 0 0 20px rgba(220, 38, 38, 0.2);
    }

    /* Navigation Enhancements */
    .nav-link-modern {
        @apply relative px-4 py-2 text-gray-700 font-medium transition-all duration-300 hover:text-primary-600;
    }

    .nav-link-modern::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 50%;
        width: 0;
        height: 2px;
        background: linear-gradient(90deg, #dc2626, #fbbf24);
        transition: all 0.3s ease;
        transform: translateX(-50%);
    }

    .nav-link-modern:hover::after,
    .nav-link-modern.active::after {
        width: 100%;
    }

    /* Carousel Styles */
    .carousel-container {
        scroll-snap-type: x mandatory;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .carousel-container::-webkit-scrollbar {
        display: none;
    }

    .carousel-item {
        scroll-snap-align: start;
        flex-shrink: 0;
    }

    /* Progress Bar */
    .progress-bar {
        @apply bg-gray-200 rounded-full h-2 overflow-hidden;
    }

    .progress-fill {
        @apply bg-gradient-to-r from-primary-500 to-primary-600 h-full rounded-full transition-all duration-500 ease-out;
    }

    /* Toggle Switch */
    .toggle-switch {
        @apply relative inline-block w-12 h-6 bg-gray-300 rounded-full cursor-pointer transition-colors duration-300;
    }

    .toggle-switch.active {
        @apply bg-primary-500;
    }

    .toggle-switch::after {
        content: '';
        @apply absolute top-1 left-1 w-4 h-4 bg-white rounded-full transition-transform duration-300;
    }

    .toggle-switch.active::after {
        transform: translateX(24px);
    }

    /* Modal Styles */
    .modal-backdrop {
        @apply fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center p-4;
        animation: fadeIn 0.3s ease-out;
    }

    .modal-content {
        @apply bg-white rounded-2xl shadow-2xl max-w-lg w-full max-h-screen overflow-y-auto;
        animation: slideUp 0.3s ease-out;
    }

    /* Masonry Grid */
    .masonry-grid {
        column-count: 1;
        column-gap: 1rem;
    }

    @media (min-width: 640px) {
        .masonry-grid {
            column-count: 2;
        }
    }

    @media (min-width: 768px) {
        .masonry-grid {
            column-count: 3;
        }
    }

    @media (min-width: 1024px) {
        .masonry-grid {
            column-count: 4;
        }
    }

    .masonry-item {
        break-inside: avoid;
        margin-bottom: 1rem;
    }

    /* 3D Tilt Effect */
    .tilt-3d {
        transform-style: preserve-3d;
        transition: transform 0.3s ease;
    }

    .tilt-3d:hover {
        transform: perspective(1000px) rotateX(5deg) rotateY(5deg);
    }

    /* Floating Animation */
    .float {
        animation: float 3s ease-in-out infinite;
    }

    /* Pulse Animation */
    .pulse-slow {
        animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    /* Bounce Animation for Cart */
    .bounce-cart {
        animation: bounceCart 0.6s ease-in-out;
    }

    /* Confetti Animation */
    .confetti {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 9999;
    }

    .confetti-piece {
        position: absolute;
        width: 10px;
        height: 10px;
        background: #dc2626;
        animation: confettiFall 3s linear infinite;
    }

    /* Sidebar Animations */
    .sidebar-slide {
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
    }

    .sidebar-slide.open {
        transform: translateX(0);
    }

    /* Dark Theme Styles */
    .dark .card-dark {
        @apply bg-gray-800 text-white;
    }

    .dark .glass-dark {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
}

/* Keyframe Animations */
@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes gradientText {
    0% {
        background-position: 0% 50%;
    }
    100% {
        background-position: 100% 50%;
    }
}

@keyframes loading {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes bounceCart {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes confettiFall {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(360deg);
        opacity: 0;
    }
}

@keyframes countUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes flipCard {
    0% {
        transform: rotateY(0);
    }
    50% {
        transform: rotateY(-90deg);
    }
    100% {
        transform: rotateY(0);
    }
}

@keyframes logoSpin {
    from {
        transform: rotate(0deg) scale(0.5);
        opacity: 0;
    }
    to {
        transform: rotate(360deg) scale(1);
        opacity: 1;
    }
}

@keyframes rippleEffect {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes zoomIn {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes heartbeat {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* Utility Animation Classes */
.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.animate-slide-in-up {
    animation: slideInUp 0.6s ease-out;
}

.animate-zoom-in {
    animation: zoomIn 0.5s ease-out;
}

.animate-count-up {
    animation: countUp 0.8s ease-out;
}

.animate-flip-card {
    animation: flipCard 0.6s ease-in-out;
}

.animate-logo-spin {
    animation: logoSpin 1.5s ease-out;
}

.animate-ripple {
    animation: rippleEffect 0.6s ease-out;
}

.animate-heartbeat {
    animation: heartbeat 1.5s ease-in-out infinite;
}

/* Responsive Utilities */
@media (max-width: 640px) {
    .card-modern:hover {
        transform: translateY(-4px);
    }

    .hover-lift:hover {
        transform: translateY(-3px);
    }
}

/* Cursor Styles */
.cursor-ripple {
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.cursor-ripple::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: rippleEffect 0.6s linear;
    pointer-events: none;
}

/* Intersection Observer Animation Classes */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.fade-in-up.animate {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-30px);
    transition: all 0.6s ease-out;
}

.fade-in-left.animate {
    opacity: 1;
    transform: translateX(0);
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px);
    transition: all 0.6s ease-out;
}

.fade-in-right.animate {
    opacity: 1;
    transform: translateX(0);
}

.scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.6s ease-out;
}

.scale-in.animate {
    opacity: 1;
    transform: scale(1);
}

/* Hero Section Enhancements */
.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: floatParticle 6s ease-in-out infinite;
}

.particle-1 {
    width: 4px;
    height: 4px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.particle-2 {
    width: 6px;
    height: 6px;
    top: 60%;
    left: 80%;
    animation-delay: 2s;
}

.particle-3 {
    width: 3px;
    height: 3px;
    top: 80%;
    left: 20%;
    animation-delay: 4s;
}

.particle-4 {
    width: 5px;
    height: 5px;
    top: 30%;
    left: 70%;
    animation-delay: 1s;
}

.particle-5 {
    width: 4px;
    height: 4px;
    top: 70%;
    left: 50%;
    animation-delay: 3s;
}

@keyframes floatParticle {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
        opacity: 0.3;
    }
    25% {
        transform: translateY(-20px) translateX(10px);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-10px) translateX(-5px);
        opacity: 0.5;
    }
    75% {
        transform: translateY(-30px) translateX(15px);
        opacity: 0.8;
    }
}

/* Logo Intro Animation */
#logo-intro {
    animation: logoIntroFade 3s ease-in-out forwards;
    animation-delay: 2s;
}

@keyframes logoIntroFade {
    0% {
        opacity: 1;
        visibility: visible;
    }
    90% {
        opacity: 1;
        visibility: visible;
    }
    100% {
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
    }
}

/* Enhanced Ripple Effect for Hero */
.cursor-ripple {
    position: relative;
    overflow: hidden;
}

.cursor-ripple::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    transform: scale(0);
    pointer-events: none;
    z-index: 1;
}

.cursor-ripple:active::after {
    animation: heroRipple 0.8s ease-out;
}

@keyframes heroRipple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(20);
        opacity: 0;
    }
}

/* Shake Animation for Form Validation */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

.animate-shake {
    animation: shake 0.6s ease-in-out;
}

/* Enhanced Gradient Text */
.text-gradient-animated {
    background: linear-gradient(-45deg, #dc2626, #fbbf24, #10b981, #3b82f6);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease infinite;
}

/* Glow Effects */
.glow-red {
    box-shadow: 0 0 20px rgba(220, 38, 38, 0.5);
}

.glow-yellow {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
}

.glow-white {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

/* Ripple Effect Styles */
.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: rippleEffect 0.6s linear;
    pointer-events: none;
}

/* Enhanced Card Flip */
.flip-card {
    background-color: transparent;
    perspective: 1000px;
}

.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

.flip-card:hover .flip-card-inner {
    transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-radius: 16px;
}

.flip-card-back {
    transform: rotateY(180deg);
}

/* Utility Classes */
.scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Featured Card Enhancement */
.featured-card {
    position: relative;
}

.featured-card::before {
    content: '⭐ Featured';
    position: absolute;
    top: -8px;
    left: 16px;
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    z-index: 10;
}

/* Enhanced Hover States */
.hover-float:hover {
    transform: translateY(-8px);
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
    transition: transform 0.3s ease;
}

.hover-brightness:hover {
    filter: brightness(1.1);
    transition: filter 0.3s ease;
}

/* Loading States */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

/* Social Media Icons Enhancement */
.social-icon {
    @apply w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110;
}

.social-icon.facebook {
    @apply bg-blue-600 hover:bg-blue-700 text-white;
}

.social-icon.twitter {
    @apply bg-sky-500 hover:bg-sky-600 text-white;
}

.social-icon.instagram {
    @apply bg-gradient-to-br from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white;
}

.social-icon.youtube {
    @apply bg-red-600 hover:bg-red-700 text-white;
}

/* Notification Badge */
.notification-badge {
    @apply absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold animate-pulse;
}

/* Custom Scrollbar for Desktop */
@media (min-width: 768px) {
    .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
    }

    .custom-scrollbar::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #dc2626;
        border-radius: 3px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #b91c1c;
    }
}
