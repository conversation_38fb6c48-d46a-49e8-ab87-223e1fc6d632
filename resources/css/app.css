@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Mbuni FC Styles */
@layer components {
    .btn-primary {
        @apply bg-primary-600 hover:bg-primary-700 text-white font-semibold py-2 px-4 rounded-lg transition duration-200 ease-in-out;
    }

    .btn-secondary {
        @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded-lg transition duration-200 ease-in-out;
    }

    .btn-outline {
        @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-semibold py-2 px-4 rounded-lg transition duration-200 ease-in-out;
    }

    .card {
        @apply bg-white rounded-lg shadow-md overflow-hidden;
    }

    .card-header {
        @apply px-6 py-4 border-b border-gray-200;
    }

    .card-body {
        @apply px-6 py-4;
    }

    .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }

    .badge-primary {
        @apply bg-primary-100 text-primary-800;
    }

    .badge-success {
        @apply bg-green-100 text-green-800;
    }

    .badge-warning {
        @apply bg-yellow-100 text-yellow-800;
    }

    .badge-danger {
        @apply bg-red-100 text-red-800;
    }

    .hero-gradient {
        background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
    }

    .text-gradient {
        background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
}
