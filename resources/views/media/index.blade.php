@extends('layouts.main')

@section('title', 'Fan Zone & Media Gallery')
@section('description', 'Explore photos, videos, and interactive content from Mbuni FC matches, training sessions, and club events.')

@section('content')
    <!-- Enhanced Fan Zone Header -->
    <section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white py-20 overflow-hidden">
        <!-- Background Effects -->
        <div class="absolute inset-0">
            <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"></div>
            <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
            <!-- Animated Media Icons -->
            <div class="absolute top-10 left-10 text-white/10 text-4xl animate-bounce">📸</div>
            <div class="absolute top-20 right-20 text-white/10 text-3xl animate-bounce" style="animation-delay: 1s;">🎥</div>
            <div class="absolute bottom-20 left-20 text-white/10 text-3xl animate-bounce" style="animation-delay: 2s;">🎉</div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center fade-in-up">
                <h1 class="text-5xl md:text-6xl font-black mb-6">
                    Fan <span class="text-gradient bg-gradient-to-r from-yellow-300 to-yellow-500 bg-clip-text text-transparent">Zone</span>
                </h1>
                <p class="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed mb-8">
                    Immerse yourself in the world of Mbuni FC - photos, videos, and exclusive fan content
                </p>

                <!-- Media Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="{{ $mediaItems->where('type', 'image')->count() }}">0</div>
                        <div class="text-sm text-white/70">Photos</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="{{ $mediaItems->where('type', 'video')->count() }}">0</div>
                        <div class="text-sm text-white/70">Videos</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="50">0</div>
                        <div class="text-sm text-white/70">Events</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="1000">0</div>
                        <div class="text-sm text-white/70">Fans</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Filters -->
    <section class="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 py-8 sticky top-20 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <!-- Enhanced Search -->
                <form method="GET" class="flex gap-3 fade-in-left">
                    <div class="relative flex-1 max-w-md">
                        <input type="text"
                               name="search"
                               value="{{ request('search') }}"
                               placeholder="Search photos, videos, events..."
                               class="form-input-glow w-full pl-12 pr-4 py-3 text-sm">
                        <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        @if(request('type'))
                            <input type="hidden" name="type" value="{{ request('type') }}">
                        @endif
                    </div>
                    <button type="submit" class="btn-primary hover-glow">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Search
                        </span>
                    </button>
                    @if(request('search') || request('type'))
                        <a href="{{ route('media.index') }}" class="btn-secondary hover:scale-105 transition-transform duration-300">
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Clear
                            </span>
                        </a>
                    @endif
                </form>

                <!-- Enhanced Type Filter -->
                <div class="flex flex-wrap gap-3 fade-in-right">
                    <a href="{{ route('media.index') }}"
                       class="px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 hover:scale-105 {{ !request('type') ? 'bg-gradient-to-r from-primary-600 to-primary-700 text-white shadow-lg' : 'neomorphism text-gray-700 hover:text-primary-600' }}">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                            All Media
                        </span>
                    </a>
                    <a href="{{ route('media.index', ['type' => 'images']) }}"
                       class="px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 hover:scale-105 {{ request('type') === 'images' ? 'bg-gradient-to-r from-primary-600 to-primary-700 text-white shadow-lg' : 'neomorphism text-gray-700 hover:text-primary-600' }}">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Photos
                        </span>
                    </a>
                    <a href="{{ route('media.index', ['type' => 'videos']) }}"
                       class="px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 hover:scale-105 {{ request('type') === 'videos' ? 'bg-gradient-to-r from-primary-600 to-primary-700 text-white shadow-lg' : 'neomorphism text-gray-700 hover:text-primary-600' }}">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                            Videos
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Masonry Gallery -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
        <!-- Background Decorations -->
        <div class="absolute top-0 right-0 w-96 h-96 bg-primary-100 rounded-full blur-3xl opacity-30 translate-x-48 -translate-y-48"></div>
        <div class="absolute bottom-0 left-0 w-80 h-80 bg-yellow-100 rounded-full blur-3xl opacity-30 -translate-x-40 translate-y-40"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            @if($mediaItems->count() > 0)
                <!-- Results Info -->
                <div class="text-center mb-12 fade-in-up">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">
                        @if(request('search'))
                            Search Results for "{{ request('search') }}"
                        @elseif(request('type') === 'images')
                            Photo Gallery
                        @elseif(request('type') === 'videos')
                            Video Collection
                        @else
                            Media Gallery
                        @endif
                    </h2>
                    <p class="text-gray-600">{{ $mediaItems->total() }} {{ Str::plural('item', $mediaItems->total()) }} found</p>
                </div>

                <!-- Masonry Grid (Pinterest Style) -->
                <div class="masonry-grid columns-1 sm:columns-2 lg:columns-3 xl:columns-4 gap-6 space-y-6">
                    @foreach($mediaItems as $index => $item)
                        <div class="break-inside-avoid mb-6 fade-in-up" style="animation-delay: {{ ($index % 8) * 0.1 }}s;">
                            <div class="glass-card overflow-hidden hover-lift group cursor-pointer"
                                 onclick="openLightbox({{ $index }}, '{{ $item->type }}', '{{ $item->file_url_full }}', '{{ $item->title }}', '{{ $item->description ?? '' }}')">

                                <!-- Media Content -->
                                <div class="relative overflow-hidden">
                                    @if($item->type === 'image')
                                        <!-- Image with random heights for masonry effect -->
                                        @php
                                            $heights = ['h-48', 'h-56', 'h-64', 'h-72', 'h-80'];
                                            $randomHeight = $heights[($index + $item->id) % count($heights)];
                                        @endphp
                                        <div class="image-zoom {{ $randomHeight }} overflow-hidden">
                                            <img src="{{ $item->file_url_full }}"
                                                 alt="{{ $item->title }}"
                                                 class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                                                 loading="lazy">
                                            <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                        </div>

                                        <!-- Image Overlay -->
                                        <div class="absolute inset-0 bg-gradient-to-t from-primary-900/90 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                                            <div class="p-4 w-full">
                                                <div class="flex items-center justify-between">
                                                    <div class="flex items-center text-white">
                                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                        </svg>
                                                        <span class="text-sm">View</span>
                                                    </div>
                                                    <button class="text-white hover:text-yellow-300 transition-colors" onclick="event.stopPropagation(); shareMedia('{{ $item->title }}', '{{ $item->file_url_full }}')">
                                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    @else
                                        <!-- Video Tile with Autoplay on Hover -->
                                        <div class="relative h-64 bg-gray-900 overflow-hidden">
                                            @if(str_contains($item->file_url, 'youtube') || str_contains($item->file_url, 'youtu.be'))
                                                @php
                                                    $videoId = null;
                                                    if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/', $item->file_url, $matches)) {
                                                        $videoId = $matches[1];
                                                    }
                                                @endphp
                                                @if($videoId)
                                                    <img src="https://img.youtube.com/vi/{{ $videoId }}/maxresdefault.jpg"
                                                         alt="{{ $item->title }}"
                                                         class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
                                                @endif
                                            @else
                                                <video class="w-full h-full object-cover video-preview"
                                                       preload="metadata"
                                                       muted
                                                       loop
                                                       onmouseenter="this.play()"
                                                       onmouseleave="this.pause(); this.currentTime = 0;">
                                                    <source src="{{ $item->file_url_full }}#t=0.5" type="video/mp4">
                                                </video>
                                            @endif

                                            <!-- Video Overlay -->
                                            <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

                                            <!-- Play Button -->
                                            <div class="absolute inset-0 flex items-center justify-center">
                                                <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center group-hover:bg-white/30 transition-all duration-300 hover-glow">
                                                    <svg class="w-8 h-8 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M8 5v14l11-7z"/>
                                                    </svg>
                                                </div>
                                            </div>

                                            <!-- Video Info Overlay -->
                                            <div class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/80 to-transparent">
                                                <div class="flex items-center justify-between text-white">
                                                    <div class="flex items-center">
                                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                        </svg>
                                                        <span class="text-sm">Play Video</span>
                                                    </div>
                                                    <button class="text-white hover:text-yellow-300 transition-colors" onclick="event.stopPropagation(); shareMedia('{{ $item->title }}', '{{ $item->file_url_full }}')">
                                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    @endif

                                    <!-- Type Badge -->
                                    <div class="absolute top-4 left-4">
                                        <span class="glass-card px-3 py-1 text-white text-sm font-semibold">
                                            {{ $item->type === 'image' ? '📸 Photo' : '🎥 Video' }}
                                        </span>
                                    </div>
                                </div>

                                <!-- Content Info -->
                                <div class="p-4">
                                    <h3 class="font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-300 line-clamp-2">
                                        {{ $item->title }}
                                    </h3>
                                    @if($item->description)
                                        <p class="text-gray-600 text-sm line-clamp-3 mb-3">{{ $item->description }}</p>
                                    @endif

                                    <!-- Meta Info -->
                                    <div class="flex items-center justify-between text-xs text-gray-500">
                                        <span>{{ $item->created_at->format('M d, Y') }}</span>
                                        <div class="flex items-center space-x-3">
                                            <span class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                                {{ rand(50, 500) }}
                                            </span>
                                            <span class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                                </svg>
                                                {{ rand(10, 100) }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                <!-- Enhanced Pagination -->
                @if($mediaItems->hasPages())
                    <div class="mt-16 fade-in-up">
                        <div class="flex justify-center">
                            {{ $mediaItems->appends(request()->query())->links('pagination::tailwind') }}
                        </div>
                    </div>
                @endif
            @else
                <!-- Enhanced Empty State -->
                <div class="text-center py-20 fade-in-up">
                    <div class="max-w-md mx-auto">
                        <div class="relative mb-8">
                            <div class="w-32 h-32 mx-auto bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                                <svg class="h-16 w-16 text-gray-400 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        </div>
                        <h3 class="text-3xl font-bold text-gray-900 mb-4">No Media Found</h3>
                        <p class="text-lg text-gray-600 mb-8">Check back soon for exciting photos and videos!</p>
                    </div>
                </div>
            @endif
        </div>
    </section>

    <!-- Interactive Fan Polls -->
    <section class="py-16 bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white relative overflow-hidden">
        <div class="absolute inset-0">
            <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"></div>
            <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
        </div>

        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center mb-12 fade-in-up">
                <h3 class="text-3xl font-bold mb-4">Fan Voice</h3>
                <p class="text-xl text-primary-100">Share your opinion and see what other fans think</p>
            </div>

            <div class="glass-card p-8 fade-in-up">
                <h4 class="text-2xl font-bold text-white mb-6">Who was the Man of the Match in our last game?</h4>

                <div class="space-y-4 mb-6">
                    <div class="poll-option cursor-pointer" onclick="vote(1, this)">
                        <div class="flex items-center justify-between p-4 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
                            <span class="font-semibold">John Mbwana (Forward)</span>
                            <div class="flex items-center">
                                <div class="w-32 h-2 bg-white/20 rounded-full mr-3">
                                    <div class="h-full bg-yellow-400 rounded-full transition-all duration-500" style="width: 45%"></div>
                                </div>
                                <span class="text-sm">45%</span>
                            </div>
                        </div>
                    </div>

                    <div class="poll-option cursor-pointer" onclick="vote(2, this)">
                        <div class="flex items-center justify-between p-4 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
                            <span class="font-semibold">Peter Mwanga (Midfielder)</span>
                            <div class="flex items-center">
                                <div class="w-32 h-2 bg-white/20 rounded-full mr-3">
                                    <div class="h-full bg-yellow-400 rounded-full transition-all duration-500" style="width: 30%"></div>
                                </div>
                                <span class="text-sm">30%</span>
                            </div>
                        </div>
                    </div>

                    <div class="poll-option cursor-pointer" onclick="vote(3, this)">
                        <div class="flex items-center justify-between p-4 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
                            <span class="font-semibold">David Kimaro (Goalkeeper)</span>
                            <div class="flex items-center">
                                <div class="w-32 h-2 bg-white/20 rounded-full mr-3">
                                    <div class="h-full bg-yellow-400 rounded-full transition-all duration-500" style="width: 25%"></div>
                                </div>
                                <span class="text-sm">25%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <p class="text-primary-200 text-sm mb-4">1,234 fans have voted</p>
                    <button class="btn-primary bg-white text-primary-600 hover:bg-gray-100 hover:text-primary-700" onclick="showResults()">
                        View Detailed Results
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Lightbox Modal -->
    <div id="lightbox" class="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <button onclick="closeLightbox()" class="absolute top-4 right-4 text-white hover:text-gray-300 z-10">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>

            <div class="max-w-4xl w-full">
                <div id="lightbox-content" class="text-center"></div>
                <div class="mt-4 text-center">
                    <h3 id="lightbox-title" class="text-xl font-bold text-white mb-2"></h3>
                    <p id="lightbox-description" class="text-gray-300"></p>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    function openLightbox(index, type, url, title, description) {
        document.getElementById('lightbox').classList.remove('hidden');
        document.getElementById('lightbox-title').textContent = title;
        document.getElementById('lightbox-description').textContent = description;

        const content = document.getElementById('lightbox-content');

        if (type === 'image') {
            content.innerHTML = `<img src="${url}" alt="${title}" class="max-w-full max-h-[80vh] object-contain mx-auto">`;
        } else {
            if (url.includes('youtube') || url.includes('youtu.be')) {
                const videoId = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/)?.[1];
                if (videoId) {
                    content.innerHTML = `<iframe src="https://www.youtube.com/embed/${videoId}" class="w-full h-96 mx-auto" frameborder="0" allowfullscreen></iframe>`;
                }
            } else {
                content.innerHTML = `<video controls class="max-w-full max-h-[80vh] mx-auto"><source src="${url}" type="video/mp4"></video>`;
            }
        }

        document.body.style.overflow = 'hidden';
    }

    function closeLightbox() {
        document.getElementById('lightbox').classList.add('hidden');
        document.body.style.overflow = 'auto';
    }

    function shareMedia(title, url) {
        if (navigator.share) {
            navigator.share({
                title: title + ' - Mbuni FC',
                text: 'Check out this from Mbuni FC: ' + title,
                url: url
            });
        } else {
            navigator.clipboard.writeText(url).then(() => {
                alert('Media link copied to clipboard!');
            });
        }
    }

    function vote(optionId, element) {
        element.classList.add('animate-pulse');
        setTimeout(() => {
            element.classList.remove('animate-pulse');
            alert('Thank you for voting! Results updated.');
        }, 500);
    }

    function showResults() {
        alert('Detailed poll results would be shown here in a modal or new page.');
    }

    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeLightbox();
        }
    });

    document.getElementById('lightbox').addEventListener('click', function(e) {
        if (e.target === this) {
            closeLightbox();
        }
    });
</script>
@endpush
