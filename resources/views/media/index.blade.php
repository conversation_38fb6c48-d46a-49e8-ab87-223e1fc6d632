@extends('layouts.main')

@section('title', 'Media Gallery')
@section('description', 'Browse photos and videos from Mbuni FC matches, training sessions, and club events.')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <section class="bg-primary-600 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl font-bold mb-4">Media Gallery</h1>
                <p class="text-xl text-gray-100">Photos and videos from matches, training, and club events</p>
            </div>
        </div>
    </section>

    <!-- Filters -->
    <section class="py-8 bg-white border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
                <!-- Search -->
                <form method="GET" class="flex-1 max-w-md">
                    <input type="text" 
                           name="search" 
                           value="{{ request('search') }}"
                           placeholder="Search media..." 
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                    @if(request('type'))
                        <input type="hidden" name="type" value="{{ request('type') }}">
                    @endif
                </form>

                <!-- Type Filter -->
                <div class="flex gap-2">
                    <a href="{{ route('media.index') }}" 
                       class="px-4 py-2 rounded-lg transition-colors {{ !request('type') ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                        All
                    </a>
                    <a href="{{ route('media.index', ['type' => 'images']) }}" 
                       class="px-4 py-2 rounded-lg transition-colors {{ request('type') === 'images' ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                        Photos
                    </a>
                    <a href="{{ route('media.index', ['type' => 'videos']) }}" 
                       class="px-4 py-2 rounded-lg transition-colors {{ request('type') === 'videos' ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                        Videos
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Media Grid -->
    <section class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($media->count() > 0)
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    @foreach($media as $item)
                        <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow group">
                            <div class="relative aspect-w-16 aspect-h-9 bg-gray-200">
                                @if($item->file_type === 'image')
                                    <img src="{{ $item->file_url_full }}" 
                                         alt="{{ $item->title }}" 
                                         class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                                @else
                                    <!-- Video Thumbnail -->
                                    <div class="w-full h-48 bg-gray-900 flex items-center justify-center relative">
                                        @if(str_contains($item->file_url, 'youtube') || str_contains($item->file_url, 'youtu.be'))
                                            @php
                                                $videoId = null;
                                                if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/', $item->file_url, $matches)) {
                                                    $videoId = $matches[1];
                                                }
                                            @endphp
                                            @if($videoId)
                                                <img src="https://img.youtube.com/vi/{{ $videoId }}/maxresdefault.jpg" 
                                                     alt="{{ $item->title }}" 
                                                     class="w-full h-48 object-cover">
                                            @endif
                                        @else
                                            <video class="w-full h-48 object-cover" preload="metadata">
                                                <source src="{{ $item->file_url_full }}#t=0.5" type="video/mp4">
                                            </video>
                                        @endif
                                        
                                        <!-- Play Button Overlay -->
                                        <div class="absolute inset-0 flex items-center justify-center">
                                            <div class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center group-hover:bg-opacity-100 transition-all">
                                                <svg class="w-8 h-8 text-primary-600 ml-1" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M8 5v14l11-7z"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                <!-- Media Type Badge -->
                                <div class="absolute top-2 right-2">
                                    <span class="px-2 py-1 text-xs font-medium bg-black bg-opacity-70 text-white rounded-full">
                                        {{ $item->file_type === 'image' ? 'Photo' : 'Video' }}
                                    </span>
                                </div>
                            </div>

                            <!-- Media Info -->
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">{{ $item->title }}</h3>
                                @if($item->caption)
                                    <p class="text-gray-600 text-sm mb-3 line-clamp-3">{{ $item->caption }}</p>
                                @endif
                                
                                <div class="flex items-center justify-between text-sm text-gray-500">
                                    <span>{{ $item->created_at->format('M d, Y') }}</span>
                                    @if($item->uploader)
                                        <span>By {{ $item->uploader->name }}</span>
                                    @endif
                                </div>
                            </div>

                            <!-- Click to View -->
                            <a href="{{ $item->file_url_full }}" 
                               target="_blank" 
                               class="absolute inset-0 z-10"
                               @if($item->file_type === 'image')
                                   data-lightbox="gallery" 
                                   data-title="{{ $item->title }}"
                               @endif>
                                <span class="sr-only">View {{ $item->title }}</span>
                            </a>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                @if($media->hasPages())
                    <div class="mt-12">
                        {{ $media->appends(request()->query())->links() }}
                    </div>
                @endif
            @else
                <!-- Empty State -->
                <div class="text-center py-16">
                    <svg class="w-24 h-24 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="text-xl font-medium text-gray-900 mb-2">No media found</h3>
                    <p class="text-gray-600 mb-6">
                        @if(request()->hasAny(['search', 'type']))
                            Try adjusting your search or filter criteria.
                        @else
                            Our media gallery is currently being updated. Check back soon for photos and videos!
                        @endif
                    </p>
                    @if(request()->hasAny(['search', 'type']))
                        <a href="{{ route('media.index') }}" class="btn-primary">
                            Clear Filters
                        </a>
                    @endif
                </div>
            @endif
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-16 bg-primary-600 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold mb-4">Follow Us for More</h2>
            <p class="text-xl text-gray-100 mb-8">
                Stay updated with the latest photos and videos from Mbuni FC
            </p>
            <div class="flex justify-center space-x-6">
                <a href="#" class="text-white hover:text-yellow-300 transition-colors">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                    </svg>
                </a>
                <a href="#" class="text-white hover:text-yellow-300 transition-colors">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                    </svg>
                </a>
                <a href="#" class="text-white hover:text-yellow-300 transition-colors">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
                    </svg>
                </a>
            </div>
        </div>
    </section>
</div>

<!-- Lightbox for images -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Simple lightbox functionality
    const imageLinks = document.querySelectorAll('[data-lightbox="gallery"]');
    
    imageLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Create lightbox overlay
            const overlay = document.createElement('div');
            overlay.className = 'fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4';
            overlay.onclick = () => overlay.remove();
            
            // Create image
            const img = document.createElement('img');
            img.src = this.href;
            img.className = 'max-w-full max-h-full object-contain';
            img.alt = this.dataset.title || '';
            
            overlay.appendChild(img);
            document.body.appendChild(overlay);
        });
    });
});
</script>
@endsection
