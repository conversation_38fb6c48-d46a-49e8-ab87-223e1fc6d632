@extends('layouts.main')

@section('title', $news->title)
@section('description', $news->excerpt)

@section('content')
    <!-- Breadcrumb -->
    <nav class="bg-gray-100 py-4">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center space-x-2 text-sm">
                <a href="{{ route('home') }}" class="text-gray-500 hover:text-primary-600">Home</a>
                <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <a href="{{ route('news.index') }}" class="text-gray-500 hover:text-primary-600">News</a>
                <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <span class="text-gray-900">{{ $news->title }}</span>
            </div>
        </div>
    </nav>

    <!-- Article Content -->
    <article class="py-16 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Article Header -->
            <header class="mb-8">
                <div class="flex items-center gap-4 mb-4">
                    <span class="badge badge-primary">{{ $news->category_name }}</span>
                    <time class="text-gray-500">{{ $news->published_at->format('F d, Y \a\t H:i') }}</time>
                </div>
                
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">{{ $news->title }}</h1>
                
                <div class="flex items-center text-gray-600">
                    <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    By {{ $news->author->name }}
                </div>
            </header>

            <!-- Featured Image -->
            @if($news->image)
                <div class="mb-8">
                    <img src="{{ $news->image_url }}" alt="{{ $news->title }}" class="w-full h-96 object-cover rounded-lg shadow-lg">
                </div>
            @endif

            <!-- Article Body -->
            <div class="prose prose-lg max-w-none">
                {!! $news->body !!}
            </div>

            <!-- Article Footer -->
            <footer class="mt-12 pt-8 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-600">Share this article:</span>
                        <div class="flex space-x-2">
                            <a href="https://twitter.com/intent/tweet?text={{ urlencode($news->title) }}&url={{ urlencode(request()->url()) }}" 
                               target="_blank" 
                               class="text-blue-500 hover:text-blue-600">
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                                </svg>
                            </a>
                            <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->url()) }}" 
                               target="_blank" 
                               class="text-blue-600 hover:text-blue-700">
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                    
                    <a href="{{ route('news.index') }}" class="btn-outline">
                        ← Back to News
                    </a>
                </div>
            </footer>
        </div>
    </article>

    <!-- Related Articles -->
    @if($relatedNews->count() > 0)
        <section class="py-16 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-8">Related Articles</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    @foreach($relatedNews as $article)
                        <article class="card hover:shadow-lg transition-shadow duration-300">
                            @if($article->image)
                                <img src="{{ $article->image_url }}" alt="{{ $article->title }}" class="w-full h-48 object-cover">
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center">
                                    <svg class="h-16 w-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                    </svg>
                                </div>
                            @endif
                            
                            <div class="card-body">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="badge badge-primary">{{ $article->category_name }}</span>
                                    <span class="text-sm text-gray-500">{{ $article->published_at->format('M d') }}</span>
                                </div>
                                
                                <h3 class="text-lg font-semibold mb-2 hover:text-primary-600">
                                    <a href="{{ route('news.show', $article) }}">{{ $article->title }}</a>
                                </h3>
                                
                                <p class="text-gray-600 text-sm mb-3">{{ Str::limit($article->excerpt, 100) }}</p>
                                
                                <a href="{{ route('news.show', $article) }}" class="text-primary-600 hover:text-primary-700 font-medium text-sm">
                                    Read More →
                                </a>
                            </div>
                        </article>
                    @endforeach
                </div>
            </div>
        </section>
    @endif
@endsection
