@extends('layouts.main')

@section('title', 'News')
@section('description', 'Stay updated with the latest news from Mbuni FC. Match reports, transfers, interviews and club updates.')

@section('content')
    <!-- Page Header -->
    <section class="bg-primary-600 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">Latest News</h1>
            <p class="text-xl text-primary-100">Stay updated with everything happening at Mbuni FC</p>
        </div>
    </section>

    <!-- Filters and Search -->
    <section class="bg-white border-b py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <!-- Category Filter -->
                <div class="flex flex-wrap gap-2">
                    <a href="{{ route('news.index') }}" 
                       class="px-4 py-2 rounded-full text-sm font-medium {{ !request('category') ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                        All News
                    </a>
                    @foreach($categories as $key => $name)
                        <a href="{{ route('news.index', ['category' => $key]) }}" 
                           class="px-4 py-2 rounded-full text-sm font-medium {{ request('category') === $key ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                            {{ $name }}
                        </a>
                    @endforeach
                </div>

                <!-- Search Form -->
                <form method="GET" action="{{ route('news.index') }}" class="flex gap-2">
                    @if(request('category'))
                        <input type="hidden" name="category" value="{{ request('category') }}">
                    @endif
                    <input type="text" 
                           name="search" 
                           value="{{ request('search') }}"
                           placeholder="Search news..." 
                           class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                    <button type="submit" class="btn-primary">Search</button>
                </form>
            </div>
        </div>
    </section>

    <!-- News Grid -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($news->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($news as $article)
                        <article class="card hover:shadow-lg transition-shadow duration-300">
                            @if($article->image)
                                <img src="{{ $article->image_url }}" alt="{{ $article->title }}" class="w-full h-48 object-cover">
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center">
                                    <svg class="h-16 w-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                    </svg>
                                </div>
                            @endif
                            
                            <div class="card-body">
                                <div class="flex items-center justify-between mb-3">
                                    <span class="badge badge-primary">{{ $article->category_name }}</span>
                                    <span class="text-sm text-gray-500">{{ $article->published_at->format('M d, Y') }}</span>
                                </div>
                                
                                <h2 class="text-xl font-semibold mb-3 hover:text-primary-600">
                                    <a href="{{ route('news.show', $article) }}">{{ $article->title }}</a>
                                </h2>
                                
                                <p class="text-gray-600 mb-4">{{ $article->excerpt }}</p>
                                
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-500">By {{ $article->author->name }}</span>
                                    <a href="{{ route('news.show', $article) }}" class="text-primary-600 hover:text-primary-700 font-medium">
                                        Read More →
                                    </a>
                                </div>
                            </div>
                        </article>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-12">
                    {{ $news->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-16">
                    <svg class="mx-auto h-24 w-24 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                    </svg>
                    <h3 class="text-2xl font-semibold text-gray-900 mb-2">No news found</h3>
                    <p class="text-gray-600 mb-6">
                        @if(request('search'))
                            No articles match your search criteria. Try different keywords.
                        @elseif(request('category'))
                            No articles found in this category.
                        @else
                            No news articles are available at the moment.
                        @endif
                    </p>
                    @if(request('search') || request('category'))
                        <a href="{{ route('news.index') }}" class="btn-primary">View All News</a>
                    @endif
                </div>
            @endif
        </div>
    </section>
@endsection
