@extends('layouts.main')

@section('title', 'Match Tickets')
@section('description', 'Purchase tickets for Mbuni FC matches. Secure your seat and support the team.')

@section('content')
    <!-- Enhanced Tickets Header -->
    <section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white py-20 overflow-hidden">
        <!-- Background Effects -->
        <div class="absolute inset-0">
            <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"></div>
            <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
            <!-- Animated Ticket Icons -->
            <div class="absolute top-10 left-10 text-white/10 text-4xl animate-bounce">🎫</div>
            <div class="absolute top-20 right-20 text-white/10 text-3xl animate-bounce" style="animation-delay: 1s;">🏟️</div>
            <div class="absolute bottom-20 left-20 text-white/10 text-3xl animate-bounce" style="animation-delay: 2s;">⚽</div>
        </div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center fade-in-up">
                <h1 class="text-5xl md:text-6xl font-black mb-6">
                    Match <span class="text-gradient bg-gradient-to-r from-yellow-300 to-yellow-500 bg-clip-text text-transparent">Tickets</span>
                </h1>
                <p class="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed mb-8">
                    Secure your seat and be part of the electric atmosphere at Mbuni FC matches
                </p>
                
                <!-- Ticket Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="{{ $upcomingFixtures->count() }}">0</div>
                        <div class="text-sm text-white/70">Upcoming</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="550">0</div>
                        <div class="text-sm text-white/70">Capacity</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="15">0</div>
                        <div class="text-sm text-white/70">From TSh</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="100">0</div>
                        <div class="text-sm text-white/70">% Support</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Ticket Types Section -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
        <!-- Background Decorations -->
        <div class="absolute top-0 right-0 w-96 h-96 bg-primary-100 rounded-full blur-3xl opacity-30 translate-x-48 -translate-y-48"></div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center mb-16 fade-in-up">
                <h2 class="text-4xl font-black text-gray-900 mb-6">
                    Choose Your <span class="text-gradient">Experience</span>
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    From regular seating to VIP experiences, we have the perfect ticket for every fan
                </p>
            </div>

            <!-- Ticket Types Grid -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                @foreach($ticketTypes as $type => $details)
                    <div class="tilt-3d group fade-in-up" style="animation-delay: {{ $loop->index * 0.2 }}s;">
                        <div class="neomorphism p-8 text-center hover-lift transition-all duration-500 {{ $type === 'vip' ? 'border-2 border-yellow-300' : '' }}">
                            @if($type === 'vip')
                                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                    <span class="bg-gradient-to-r from-yellow-400 to-yellow-500 text-white px-4 py-1 rounded-full text-sm font-bold">
                                        ⭐ POPULAR
                                    </span>
                                </div>
                            @endif
                            
                            <!-- Icon -->
                            <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
                                @if($type === 'regular')
                                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5h2.5a2.5 2.5 0 0 1 0 5H5m0 0h2.5a2.5 2.5 0 0 1 0 5H5"></path>
                                    </svg>
                                @elseif($type === 'vip')
                                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                    </svg>
                                @else
                                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                    </svg>
                                @endif
                            </div>

                            <!-- Title and Price -->
                            <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ $details['name'] }}</h3>
                            <div class="text-3xl font-black text-primary-600 mb-4">
                                TSh {{ number_format($details['price']) }}
                                @if($type === 'season')
                                    <span class="text-sm text-gray-500 font-normal">/season</span>
                                @else
                                    <span class="text-sm text-gray-500 font-normal">/match</span>
                                @endif
                            </div>
                            
                            <!-- Description -->
                            <p class="text-gray-600 mb-6">{{ $details['description'] }}</p>
                            
                            <!-- Features -->
                            <ul class="text-left space-y-2 mb-8">
                                @foreach($details['features'] as $feature)
                                    <li class="flex items-center text-gray-700">
                                        <svg class="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        {{ $feature }}
                                    </li>
                                @endforeach
                            </ul>
                            
                            <!-- CTA Button -->
                            @if($type === 'season')
                                <a href="{{ route('contact.index') }}" class="btn-primary w-full hover-glow">
                                    Contact for Season Pass
                                </a>
                            @else
                                <button class="btn-primary w-full hover-glow" onclick="scrollToMatches()">
                                    Select Match
                                </button>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Upcoming Matches Section -->
    @if($upcomingFixtures->count() > 0)
        <section class="py-20 bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white relative overflow-hidden" id="matches-section">
            <!-- Background Effects -->
            <div class="absolute inset-0">
                <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"></div>
                <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
            </div>
            
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="text-center mb-16 fade-in-up">
                    <h2 class="text-4xl font-black mb-6">
                        Upcoming <span class="text-yellow-300">Matches</span>
                    </h2>
                    <p class="text-xl text-white/90 max-w-2xl mx-auto">
                        Book your tickets now for these exciting fixtures
                    </p>
                </div>

                <!-- Matches Grid -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    @foreach($upcomingFixtures as $index => $fixture)
                        <div class="glass-card p-8 hover-lift fade-in-up" style="animation-delay: {{ $index * 0.2 }}s;">
                            <!-- Match Header -->
                            <div class="flex items-center justify-between mb-6">
                                <span class="badge badge-primary glass-card text-white font-semibold">
                                    {{ $fixture->competition }}
                                </span>
                                <span class="text-white/70 text-sm">
                                    {{ $fixture->match_date->format('M d, Y • H:i') }}
                                </span>
                            </div>
                            
                            <!-- Teams -->
                            <div class="flex items-center justify-between mb-6">
                                <div class="text-center flex-1">
                                    <div class="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <span class="text-2xl">{{ $fixture->home_or_away === 'home' ? '🏠' : '⚽' }}</span>
                                    </div>
                                    <div class="font-bold text-lg">{{ $fixture->home_or_away === 'home' ? 'Mbuni FC' : $fixture->opponent }}</div>
                                </div>
                                
                                <div class="text-center px-6">
                                    <div class="text-4xl font-black text-yellow-300">VS</div>
                                </div>
                                
                                <div class="text-center flex-1">
                                    <div class="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <span class="text-2xl">{{ $fixture->home_or_away === 'home' ? '🏃' : '🏠' }}</span>
                                    </div>
                                    <div class="font-bold text-lg">{{ $fixture->home_or_away === 'home' ? $fixture->opponent : 'Mbuni FC' }}</div>
                                </div>
                            </div>
                            
                            <!-- Venue -->
                            <div class="text-center mb-6">
                                <p class="text-white/80">📍 {{ $fixture->venue }}</p>
                            </div>
                            
                            <!-- Ticket Availability -->
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div class="glass-card p-4 text-center">
                                    <div class="text-lg font-bold text-yellow-300">{{ $fixture->available_tickets['regular'] ?? 0 }}</div>
                                    <div class="text-sm text-white/70">Regular Available</div>
                                </div>
                                <div class="glass-card p-4 text-center">
                                    <div class="text-lg font-bold text-yellow-300">{{ $fixture->available_tickets['vip'] ?? 0 }}</div>
                                    <div class="text-sm text-white/70">VIP Available</div>
                                </div>
                            </div>
                            
                            <!-- Book Button -->
                            <a href="{{ route('tickets.show', $fixture) }}" class="btn-primary w-full bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700">
                                <span class="flex items-center justify-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5h2.5a2.5 2.5 0 0 1 0 5H5m0 0h2.5a2.5 2.5 0 0 1 0 5H5"></path>
                                    </svg>
                                    Book Tickets
                                </span>
                            </a>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @else
        <!-- No Matches Available -->
        <section class="py-20 bg-white">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <div class="fade-in-up">
                    <div class="w-32 h-32 mx-auto bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mb-8">
                        <svg class="h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-3xl font-bold text-gray-900 mb-4">No Upcoming Matches</h3>
                    <p class="text-lg text-gray-600 mb-8">
                        There are currently no upcoming matches with tickets available. Check back soon or view our fixture list.
                    </p>
                    <a href="{{ route('fixtures.index') }}" class="btn-primary hover-glow">
                        View All Fixtures
                    </a>
                </div>
            </div>
        </section>
    @endif

    <!-- Contact Section -->
    <section class="py-16 bg-gradient-to-r from-primary-600 to-primary-700 text-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="fade-in-up">
                <h3 class="text-3xl font-bold mb-4">Need Help with Tickets?</h3>
                <p class="text-xl text-primary-100 mb-8">Our ticket support team is here to help you secure the best seats</p>
                
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="tel:+255123456789" class="btn-primary bg-white text-primary-600 hover:bg-gray-100 hover:text-primary-700">
                        <span class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            Call +255 123 456 789
                        </span>
                    </a>
                    <a href="{{ route('contact.index') }}" class="btn-outline border-white text-white hover:bg-white hover:text-primary-600">
                        <span class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            Contact Form
                        </span>
                    </a>
                </div>
                
                <p class="text-sm text-primary-200 mt-4">Available Mon-Fri: 9AM-5PM, Match days: 2 hours before kickoff</p>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
<script>
    function scrollToMatches() {
        const matchesSection = document.getElementById('matches-section');
        if (matchesSection) {
            matchesSection.scrollIntoView({ behavior: 'smooth' });
        } else {
            // If no matches section, scroll to contact
            document.querySelector('section:last-of-type').scrollIntoView({ behavior: 'smooth' });
        }
    }
</script>
@endpush
