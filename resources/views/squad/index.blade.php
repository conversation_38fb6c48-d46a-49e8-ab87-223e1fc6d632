@extends('layouts.main')

@section('title', 'Squad')
@section('description', 'Meet the Mbuni FC squad. View player profiles, positions, and statistics of our talented team.')

@section('content')
    <!-- Page Header -->
    <section class="bg-primary-600 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">Our Squad</h1>
            <p class="text-xl text-primary-100">Meet the talented players who represent Mbuni FC</p>
        </div>
    </section>

    <!-- Position Filter -->
    <section class="bg-white border-b py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-wrap gap-2 justify-center">
                <a href="{{ route('squad.index') }}" 
                   class="px-4 py-2 rounded-full text-sm font-medium {{ !request('position') ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                    All Players
                </a>
                @foreach($positions as $key => $name)
                    <a href="{{ route('squad.index', ['position' => $key]) }}" 
                       class="px-4 py-2 rounded-full text-sm font-medium {{ request('position') === $key ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }}">
                        {{ $name }}
                    </a>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Squad Grid -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($playersByPosition->count() > 0)
                @foreach($positions as $position => $positionName)
                    @if($playersByPosition->has($position) && (!request('position') || request('position') === $position))
                        <div class="mb-12">
                            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">{{ $positionName }}</h2>
                            
                            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                                @foreach($playersByPosition[$position] as $player)
                                    <div class="card hover:shadow-lg transition-all duration-300 group cursor-pointer" 
                                         onclick="openPlayerModal({{ $player->id }})">
                                        <!-- Player Photo -->
                                        <div class="relative overflow-hidden">
                                            @if($player->photo)
                                                <img src="{{ $player->photo_url }}" alt="{{ $player->name }}" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                                            @else
                                                <div class="w-full h-64 bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center group-hover:from-primary-600 group-hover:to-primary-800 transition-colors duration-300">
                                                    <svg class="h-24 w-24 text-white opacity-75" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                    </svg>
                                                </div>
                                            @endif
                                            
                                            <!-- Jersey Number Overlay -->
                                            <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full w-12 h-12 flex items-center justify-center">
                                                <span class="text-xl font-bold text-primary-600">{{ $player->shirt_number }}</span>
                                            </div>
                                        </div>

                                        <!-- Player Info -->
                                        <div class="card-body text-center">
                                            <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ $player->name }}</h3>
                                            <p class="text-primary-600 font-medium mb-2">{{ $player->position_name }}</p>
                                            <p class="text-gray-500 text-sm">{{ $player->nationality }}</p>
                                            
                                            @if($player->stats)
                                                <div class="mt-4 grid grid-cols-3 gap-2 text-center">
                                                    @if(isset($player->stats['goals']))
                                                        <div>
                                                            <div class="text-lg font-bold text-primary-600">{{ $player->stats['goals'] }}</div>
                                                            <div class="text-xs text-gray-500">Goals</div>
                                                        </div>
                                                    @endif
                                                    @if(isset($player->stats['assists']))
                                                        <div>
                                                            <div class="text-lg font-bold text-primary-600">{{ $player->stats['assists'] }}</div>
                                                            <div class="text-xs text-gray-500">Assists</div>
                                                        </div>
                                                    @endif
                                                    @if(isset($player->stats['appearances']))
                                                        <div>
                                                            <div class="text-lg font-bold text-primary-600">{{ $player->stats['appearances'] }}</div>
                                                            <div class="text-xs text-gray-500">Apps</div>
                                                        </div>
                                                    @endif
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                @endforeach
            @else
                <div class="text-center py-16">
                    <svg class="mx-auto h-24 w-24 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    <h3 class="text-2xl font-semibold text-gray-900 mb-2">No players found</h3>
                    <p class="text-gray-600">No players are available in the selected position.</p>
                </div>
            @endif
        </div>
    </section>

    <!-- Player Modal -->
    <div id="playerModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto">
            <div id="modalContent">
                <!-- Modal content will be loaded here -->
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    const players = @json($playersByPosition->flatten());
    
    function openPlayerModal(playerId) {
        const player = players.find(p => p.id === playerId);
        if (!player) return;
        
        const modal = document.getElementById('playerModal');
        const content = document.getElementById('modalContent');
        
        content.innerHTML = `
            <div class="relative">
                <button onclick="closePlayerModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 z-10">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                
                <div class="p-6">
                    <div class="flex flex-col md:flex-row gap-6">
                        <div class="md:w-1/3">
                            ${player.photo ? 
                                `<img src="${player.photo_url}" alt="${player.name}" class="w-full h-80 object-cover rounded-lg">` :
                                `<div class="w-full h-80 bg-gradient-to-br from-red-500 to-red-700 flex items-center justify-center rounded-lg">
                                    <svg class="h-32 w-32 text-white opacity-75" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>`
                            }
                        </div>
                        
                        <div class="md:w-2/3">
                            <div class="flex items-center gap-4 mb-4">
                                <h2 class="text-3xl font-bold text-gray-900">${player.name}</h2>
                                <span class="bg-red-600 text-white px-3 py-1 rounded-full text-lg font-bold">#${player.shirt_number}</span>
                            </div>
                            
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div>
                                    <span class="text-gray-600">Position:</span>
                                    <span class="font-semibold text-red-600 ml-2">${player.position_name}</span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Nationality:</span>
                                    <span class="font-semibold ml-2">${player.nationality}</span>
                                </div>
                            </div>
                            
                            ${player.bio ? `
                                <div class="mb-6">
                                    <h3 class="text-lg font-semibold mb-2">Biography</h3>
                                    <p class="text-gray-700">${player.bio}</p>
                                </div>
                            ` : ''}
                            
                            ${player.stats ? `
                                <div>
                                    <h3 class="text-lg font-semibold mb-4">Season Statistics</h3>
                                    <div class="grid grid-cols-3 gap-4">
                                        ${player.stats.goals !== undefined ? `
                                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                                <div class="text-2xl font-bold text-red-600">${player.stats.goals}</div>
                                                <div class="text-gray-600">Goals</div>
                                            </div>
                                        ` : ''}
                                        ${player.stats.assists !== undefined ? `
                                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                                <div class="text-2xl font-bold text-red-600">${player.stats.assists}</div>
                                                <div class="text-gray-600">Assists</div>
                                            </div>
                                        ` : ''}
                                        ${player.stats.appearances !== undefined ? `
                                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                                <div class="text-2xl font-bold text-red-600">${player.stats.appearances}</div>
                                                <div class="text-gray-600">Appearances</div>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }
    
    function closePlayerModal() {
        const modal = document.getElementById('playerModal');
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }
    
    // Close modal when clicking outside
    document.getElementById('playerModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closePlayerModal();
        }
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closePlayerModal();
        }
    });
</script>
@endpush
