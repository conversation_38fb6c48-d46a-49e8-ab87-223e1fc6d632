@extends('layouts.main')

@section('title', 'Official Store')
@section('description', 'Shop official Mbuni FC merchandise including jerseys, accessories, and fan gear.')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Enhanced Store Header -->
    <section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white py-20 overflow-hidden">
        <!-- Background Effects -->
        <div class="absolute inset-0">
            <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"></div>
            <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
            <!-- Animated Store Icons -->
            <div class="absolute top-10 left-10 text-white/10 text-4xl animate-bounce">🛍️</div>
            <div class="absolute top-20 right-20 text-white/10 text-3xl animate-bounce" style="animation-delay: 1s;">👕</div>
            <div class="absolute bottom-20 left-20 text-white/10 text-3xl animate-bounce" style="animation-delay: 2s;">⚽</div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center fade-in-up">
                <h1 class="text-5xl md:text-6xl font-black mb-6">
                    Official <span class="text-gradient bg-gradient-to-r from-yellow-300 to-yellow-500 bg-clip-text text-transparent">Store</span>
                </h1>
                <p class="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed mb-8">
                    Show your support with official Mbuni FC merchandise - jerseys, accessories, and exclusive fan gear
                </p>

                <!-- Store Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="{{ $merchandise->total() }}">0</div>
                        <div class="text-sm text-white/70">Products</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="{{ $categories->count() }}">0</div>
                        <div class="text-sm text-white/70">Categories</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="100">0</div>
                        <div class="text-sm text-white/70">% Official</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="1000">0</div>
                        <div class="text-sm text-white/70">Happy Fans</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Filters and Search -->
    <section class="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 py-8 sticky top-20 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <!-- Enhanced Search -->
                <form method="GET" class="flex gap-3 fade-in-left">
                    <div class="relative flex-1 max-w-md">
                        <input type="text"
                               name="search"
                               value="{{ request('search') }}"
                               placeholder="Search jerseys, accessories, gear..."
                               class="form-input-glow w-full pl-12 pr-4 py-3 text-sm">
                        <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>

                    <!-- Enhanced Selects -->
                    <select name="category" class="form-input-glow px-4 py-3 text-sm min-w-32">
                        <option value="">All Categories</option>
                        @foreach($categories as $category)
                            <option value="{{ $category }}" {{ request('category') === $category ? 'selected' : '' }}>
                                {{ ucfirst($category) }}
                            </option>
                        @endforeach
                    </select>

                    <select name="sort" class="form-input-glow px-4 py-3 text-sm min-w-32">
                        <option value="name" {{ request('sort') === 'name' ? 'selected' : '' }}>Name</option>
                        <option value="newest" {{ request('sort') === 'newest' ? 'selected' : '' }}>Newest</option>
                        <option value="category" {{ request('sort') === 'category' ? 'selected' : '' }}>Category</option>
                    </select>

                    <button type="submit" class="btn-primary hover-glow">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                            </svg>
                            Filter
                        </span>
                    </button>

                    @if(request('search') || request('category') || request('sort'))
                        <a href="{{ route('store.index') }}" class="btn-secondary hover:scale-105 transition-transform duration-300">
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Clear
                            </span>
                        </a>
                    @endif
                </form>

                <!-- Quick Category Filters -->
                <div class="flex flex-wrap gap-3 fade-in-right">
                    <a href="{{ route('store.index') }}"
                       class="px-4 py-2 rounded-2xl text-sm font-semibold transition-all duration-300 hover:scale-105 {{ !request('category') ? 'bg-gradient-to-r from-primary-600 to-primary-700 text-white shadow-lg' : 'neomorphism text-gray-700 hover:text-primary-600' }}">
                        All
                    </a>
                    @foreach($categories as $category)
                        <a href="{{ route('store.index', ['category' => $category]) }}"
                           class="px-4 py-2 rounded-2xl text-sm font-semibold transition-all duration-300 hover:scale-105 {{ request('category') === $category ? 'bg-gradient-to-r from-primary-600 to-primary-700 text-white shadow-lg' : 'neomorphism text-gray-700 hover:text-primary-600' }}">
                            {{ ucfirst($category) }}
                        </a>
                    @endforeach
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Products Grid -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
        <!-- Background Decorations -->
        <div class="absolute top-0 right-0 w-96 h-96 bg-primary-100 rounded-full blur-3xl opacity-30 translate-x-48 -translate-y-48"></div>
        <div class="absolute bottom-0 left-0 w-80 h-80 bg-yellow-100 rounded-full blur-3xl opacity-30 -translate-x-40 translate-y-40"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            @if($merchandise->count() > 0)
                <!-- Results Info -->
                <div class="text-center mb-12 fade-in-up">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">
                        @if(request('search'))
                            Search Results for "{{ request('search') }}"
                        @elseif(request('category'))
                            {{ ucfirst(request('category')) }} Collection
                        @else
                            Official Merchandise
                        @endif
                    </h2>
                    <p class="text-gray-600">{{ $merchandise->total() }} {{ Str::plural('product', $merchandise->total()) }} available</p>
                </div>

                <!-- Product Carousel Slider -->
                <div class="carousel-wrapper mb-12 fade-in-up" style="animation-delay: 0.2s;">
                    <div class="relative">
                        <div class="carousel-container flex gap-6 overflow-x-auto pb-4 snap-x snap-mandatory scrollbar-hide">
                            @foreach($merchandise as $index => $item)
                                <div class="carousel-item min-w-80 tilt-3d group">
                                    <div class="glass-card rounded-2xl overflow-hidden hover-lift transition-all duration-500">
                                        <!-- Product Image with Enhanced Effects -->
                                        <div class="relative overflow-hidden">
                                            @if($item->image)
                                                <div class="image-zoom h-64 overflow-hidden">
                                                    <img src="{{ $item->image_url }}"
                                                         alt="{{ $item->name }}"
                                                         class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
                                                    <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                                </div>
                                            @else
                                                <div class="h-64 bg-gradient-to-br from-primary-500 via-primary-600 to-primary-700 flex items-center justify-center relative overflow-hidden">
                                                    <div class="absolute inset-0 bg-gradient-to-br from-transparent to-black/20"></div>
                                                    <svg class="w-16 h-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                                    </svg>
                                                </div>
                                            @endif

                                            <!-- Floating Category Badge -->
                                            @if($item->category)
                                                <div class="absolute top-4 left-4">
                                                    <span class="glass-card px-3 py-1 text-white text-sm font-semibold">
                                                        {{ ucfirst($item->category) }}
                                                    </span>
                                                </div>
                                            @endif

                                            <!-- Stock Status Badge -->
                                            <div class="absolute top-4 right-4">
                                                @if($item->stock_quantity <= 0)
                                                    <span class="glass-card px-3 py-1 text-red-400 text-sm font-semibold">
                                                        ✗ Out of Stock
                                                    </span>
                                                @elseif($item->stock_quantity <= 5)
                                                    <span class="glass-card px-3 py-1 text-yellow-400 text-sm font-semibold">
                                                        ⚠ Low Stock
                                                    </span>
                                                @else
                                                    <span class="glass-card px-3 py-1 text-green-400 text-sm font-semibold">
                                                        ✓ Available
                                                    </span>
                                                @endif
                                            </div>

                                            <!-- Hover Overlay with Quick Actions -->
                                            <div class="absolute inset-0 bg-gradient-to-t from-primary-900/90 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                                                <div class="p-6 w-full">
                                                    <div class="flex space-x-3">
                                                        <a href="{{ route('store.show', $item) }}" class="flex-1 btn-primary text-center text-sm">
                                                            View Details
                                                        </a>
                                                        <button class="btn-outline border-white text-white hover:bg-white hover:text-primary-600 text-sm" onclick="shareProduct('{{ $item->name }}', '{{ route('store.show', $item) }}')">
                                                            Share
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Product Info -->
                                        <div class="p-6">
                                            <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-300 line-clamp-2">
                                                {{ $item->name }}
                                            </h3>
                                            <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ $item->description }}</p>

                                            <!-- Stock and Action Info -->
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center space-x-2">
                                                    @if($item->stock_quantity > 0)
                                                        <span class="text-green-600 font-semibold text-sm flex items-center">
                                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                            </svg>
                                                            Available
                                                        </span>
                                                    @else
                                                        <span class="text-red-600 font-semibold text-sm flex items-center">
                                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                            </svg>
                                                            Out of Stock
                                                        </span>
                                                    @endif
                                                </div>

                                                <a href="{{ route('store.show', $item) }}"
                                                   class="btn-outline hover:scale-105 transition-transform duration-300 text-sm">
                                                    View Details
                                                </a>
                                            </div>
                                        </div>

                                        <!-- Hover Glow Effect -->
                                        <div class="absolute inset-0 bg-gradient-to-t from-primary-600/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none rounded-2xl"></div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Carousel Navigation -->
                        <button class="carousel-prev absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 w-12 h-12 bg-white shadow-lg rounded-full flex items-center justify-center hover:bg-primary-50 transition-colors duration-300">
                            <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </button>
                        <button class="carousel-next absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 w-12 h-12 bg-white shadow-lg rounded-full flex items-center justify-center hover:bg-primary-50 transition-colors duration-300">
                            <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Enhanced Pagination -->
                @if($merchandise->hasPages())
                    <div class="mt-16 fade-in-up">
                        <div class="flex justify-center">
                            {{ $merchandise->appends(request()->query())->links('pagination::tailwind') }}
                        </div>
                    </div>
                @endif
            @else
                <!-- Enhanced Empty State -->
                <div class="text-center py-20 fade-in-up">
                    <div class="max-w-md mx-auto">
                        <!-- Animated Icon -->
                        <div class="relative mb-8">
                            <div class="w-32 h-32 mx-auto bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                                <svg class="h-16 w-16 text-gray-400 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                </svg>
                            </div>
                            <div class="absolute -top-2 -right-2 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm">🛍️</span>
                            </div>
                        </div>

                        <h3 class="text-3xl font-bold text-gray-900 mb-4">No Products Found</h3>
                        <p class="text-lg text-gray-600 mb-8">
                            @if(request()->hasAny(['search', 'category']))
                                We couldn't find any products matching your criteria. Try adjusting your search or browse all products.
                            @else
                                Our store is currently being updated with new merchandise. Check back soon for official Mbuni FC gear!
                            @endif
                        </p>

                        <div class="space-y-4">
                            @if(request()->hasAny(['search', 'category']))
                                <a href="{{ route('store.index') }}" class="btn-primary hover-glow inline-flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                    </svg>
                                    View All Products
                                </a>
                            @endif

                            <div class="flex justify-center space-x-4">
                                <a href="{{ route('contact.index') }}" class="btn-outline hover:scale-105 transition-transform duration-300">
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                        Contact Us
                                    </span>
                                </a>
                                <a href="{{ route('news.index') }}" class="btn-outline hover:scale-105 transition-transform duration-300">
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                        </svg>
                                        Latest News
                                    </span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </section>

    <!-- Enhanced Features Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 fade-in-up">
                <h3 class="text-3xl font-bold text-gray-900 mb-4">
                    Why Choose <span class="text-gradient">Official Merchandise</span>
                </h3>
                <p class="text-gray-600 max-w-2xl mx-auto">Experience the quality and authenticity that comes with official Mbuni FC products</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center neomorphism p-8 hover:scale-105 transition-transform duration-300 fade-in-up">
                    <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">100% Authentic</h3>
                    <p class="text-gray-600">Official Mbuni FC products with guaranteed authenticity and quality assurance</p>
                </div>

                <div class="text-center neomorphism p-8 hover:scale-105 transition-transform duration-300 fade-in-up" style="animation-delay: 0.2s;">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Support the Club</h3>
                    <p class="text-gray-600">Every purchase directly supports Mbuni FC and helps us achieve our goals</p>
                </div>

                <div class="text-center neomorphism p-8 hover:scale-105 transition-transform duration-300 fade-in-up" style="animation-delay: 0.4s;">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Easy Contact</h3>
                    <p class="text-gray-600">Get in touch for availability, orders, and pickup arrangements</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Store Newsletter Section -->
    <section class="py-16 bg-gradient-to-r from-primary-600 to-primary-700 text-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="fade-in-up">
                <h3 class="text-3xl font-bold mb-4">Get Store Updates</h3>
                <p class="text-xl text-primary-100 mb-8">Be the first to know about new merchandise, exclusive items, and special offers</p>

                <form class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                    <input type="email" placeholder="Enter your email address" class="form-input-glow flex-1 text-gray-900">
                    <button type="submit" class="btn-primary bg-white text-primary-600 hover:bg-gray-100 hover:text-primary-700">
                        <span class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                            Subscribe
                        </span>
                    </button>
                </form>

                <p class="text-sm text-primary-200 mt-4">Join 1000+ fans getting store updates. Unsubscribe anytime.</p>
            </div>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
    function shareProduct(name, url) {
        if (navigator.share) {
            navigator.share({
                title: name + ' - Mbuni FC Store',
                text: 'Check out this official Mbuni FC merchandise: ' + name,
                url: url
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(url).then(() => {
                alert('Product link copied to clipboard!');
            });
        }
    }
</script>
@endpush
