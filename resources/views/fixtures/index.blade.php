@extends('layouts.main')

@section('title', 'Fixtures & Results')
@section('description', 'View Mbuni FC fixtures, results and match schedule. Stay updated with upcoming matches and recent results.')

@section('content')
    <!-- Page Header -->
    <section class="bg-primary-600 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">Fixtures & Results</h1>
            <p class="text-xl text-primary-100">Stay updated with our match schedule and results</p>
        </div>
    </section>

    <!-- Quick Stats -->
    <section class="bg-white py-8 border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-3xl font-bold text-primary-600">{{ $upcomingFixtures->count() }}</div>
                    <div class="text-gray-600">Upcoming Matches</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-green-600">{{ $recentResults->where('score_mbuni', '>', 'score_opponent')->count() }}</div>
                    <div class="text-gray-600">Recent Wins</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-gray-600">{{ $recentResults->count() }}</div>
                    <div class="text-gray-600">Recent Matches</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Filters -->
    <section class="bg-gray-50 py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <!-- Competition Filter -->
                <div class="flex flex-wrap gap-2">
                    <a href="{{ route('fixtures.index') }}" 
                       class="px-4 py-2 rounded-full text-sm font-medium {{ !request('competition') ? 'bg-primary-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100' }}">
                        All Competitions
                    </a>
                    @foreach($competitions as $competition)
                        <a href="{{ route('fixtures.index', ['competition' => $competition]) }}" 
                           class="px-4 py-2 rounded-full text-sm font-medium {{ request('competition') === $competition ? 'bg-primary-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100' }}">
                            {{ $competition }}
                        </a>
                    @endforeach
                </div>

                <!-- Status Filter -->
                <div class="flex gap-2">
                    <a href="{{ route('fixtures.index', array_merge(request()->query(), ['status' => 'upcoming'])) }}" 
                       class="px-4 py-2 rounded-lg text-sm font-medium {{ request('status') === 'upcoming' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100' }}">
                        Upcoming
                    </a>
                    <a href="{{ route('fixtures.index', array_merge(request()->query(), ['status' => 'finished'])) }}" 
                       class="px-4 py-2 rounded-lg text-sm font-medium {{ request('status') === 'finished' ? 'bg-green-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100' }}">
                        Results
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Next Match Highlight -->
    @if($upcomingFixtures->count() > 0 && !request('status'))
        <section class="py-12 bg-gradient-to-r from-primary-600 to-primary-700 text-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center">
                    <h2 class="text-2xl font-bold mb-2">Next Match</h2>
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6 max-w-2xl mx-auto">
                        @php $nextMatch = $upcomingFixtures->first() @endphp
                        <div class="flex items-center justify-between mb-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold">{{ $nextMatch->home_or_away === 'home' ? 'Mbuni FC' : $nextMatch->opponent }}</div>
                            </div>
                            <div class="text-center">
                                <div class="text-4xl font-bold">VS</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold">{{ $nextMatch->home_or_away === 'home' ? $nextMatch->opponent : 'Mbuni FC' }}</div>
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-semibold">{{ $nextMatch->match_date->format('l, F d, Y') }}</div>
                            <div class="text-primary-200">{{ $nextMatch->match_date->format('H:i') }} • {{ $nextMatch->venue }}</div>
                            <div class="text-sm text-primary-200 mt-2">{{ $nextMatch->competition }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    @endif

    <!-- Fixtures List -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($fixtures->count() > 0)
                <div class="space-y-4">
                    @foreach($fixtures as $fixture)
                        <div class="card hover:shadow-md transition-shadow duration-200">
                            <div class="card-body">
                                <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                                    <!-- Match Info -->
                                    <div class="flex-1">
                                        <div class="flex items-center gap-4 mb-2">
                                            <span class="badge {{ $fixture->status === 'upcoming' ? 'badge-primary' : ($fixture->status === 'live' ? 'bg-red-100 text-red-800' : 'badge-success') }}">
                                                {{ ucfirst($fixture->status) }}
                                            </span>
                                            <span class="text-sm text-gray-500">{{ $fixture->competition }}</span>
                                        </div>
                                        
                                        <div class="flex items-center justify-between md:justify-start md:gap-8">
                                            <!-- Teams -->
                                            <div class="flex items-center gap-4">
                                                <div class="text-center">
                                                    <div class="font-semibold text-lg">{{ $fixture->home_or_away === 'home' ? 'Mbuni FC' : $fixture->opponent }}</div>
                                                </div>
                                                
                                                @if($fixture->status === 'finished')
                                                    <div class="text-center">
                                                        <div class="text-2xl font-bold text-primary-600">{{ $fixture->result }}</div>
                                                    </div>
                                                @else
                                                    <div class="text-center">
                                                        <div class="text-xl font-bold text-gray-400">VS</div>
                                                    </div>
                                                @endif
                                                
                                                <div class="text-center">
                                                    <div class="font-semibold text-lg">{{ $fixture->home_or_away === 'home' ? $fixture->opponent : 'Mbuni FC' }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Match Details -->
                                    <div class="text-right">
                                        <div class="font-semibold">{{ $fixture->match_date->format('M d, Y') }}</div>
                                        <div class="text-sm text-gray-500">{{ $fixture->match_date->format('H:i') }}</div>
                                        <div class="text-sm text-gray-500">{{ $fixture->venue }}</div>
                                        @if($fixture->home_or_away === 'away')
                                            <div class="text-xs text-gray-400 mt-1">(Away)</div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-12">
                    {{ $fixtures->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-16">
                    <svg class="mx-auto h-24 w-24 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="text-2xl font-semibold text-gray-900 mb-2">No fixtures found</h3>
                    <p class="text-gray-600 mb-6">
                        @if(request('competition') || request('status'))
                            No matches found with the selected filters.
                        @else
                            No fixtures are scheduled at the moment.
                        @endif
                    </p>
                    @if(request('competition') || request('status'))
                        <a href="{{ route('fixtures.index') }}" class="btn-primary">View All Fixtures</a>
                    @endif
                </div>
            @endif
        </div>
    </section>
@endsection
