@extends('layouts.main')

@section('title', 'Contact Us')
@section('description', 'Get in touch with Mbuni FC. Contact information, location, and inquiry form.')

@section('content')
    <!-- Enhanced Contact Header -->
    <section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white py-20 overflow-hidden">
        <!-- Background Effects -->
        <div class="absolute inset-0">
            <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"></div>
            <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
            <!-- Animated Contact Icons -->
            <div class="absolute top-10 left-10 text-white/10 text-4xl animate-bounce">📞</div>
            <div class="absolute top-20 right-20 text-white/10 text-3xl animate-bounce" style="animation-delay: 1s;">📧</div>
            <div class="absolute bottom-20 left-20 text-white/10 text-3xl animate-bounce" style="animation-delay: 2s;">📍</div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center fade-in-up">
                <h1 class="text-5xl md:text-6xl font-black mb-6">
                    Contact <span class="text-gradient bg-gradient-to-r from-yellow-300 to-yellow-500 bg-clip-text text-transparent">Us</span>
                </h1>
                <p class="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed mb-8">
                    We'd love to hear from you! Get in touch with questions, feedback, or just to say hello
                </p>

                <!-- Quick Contact Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="24">0</div>
                        <div class="text-sm text-white/70">Hours</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="7">0</div>
                        <div class="text-sm text-white/70">Days</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="2">0</div>
                        <div class="text-sm text-white/70">Hours Response</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-yellow-300 animate-score" data-score="100">0</div>
                        <div class="text-sm text-white/70">% Satisfaction</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Contact Information & Form -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
        <!-- Background Decorations -->
        <div class="absolute top-0 right-0 w-96 h-96 bg-primary-100 rounded-full blur-3xl opacity-30 translate-x-48 -translate-y-48"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
                <!-- Enhanced Contact Information -->
                <div class="fade-in-left">
                    <h2 class="text-4xl font-black text-gray-900 mb-8">
                        Get in <span class="text-gradient">Touch</span>
                    </h2>

                    <div class="space-y-8">
                        <!-- Address -->
                        <div class="tilt-3d group">
                            <div class="neomorphism p-6 hover-lift transition-all duration-300">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-6">
                                        <h3 class="text-xl font-bold text-gray-900 mb-2">Visit Us</h3>
                                        <p class="text-gray-600 mb-1">Sheikh Amri Abeid Memorial Stadium</p>
                                        <p class="text-gray-600 mb-3">Arusha, Tanzania</p>
                                        <button onclick="openMap()" class="text-primary-600 hover:text-primary-700 font-semibold text-sm flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                            </svg>
                                            View on Map
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Phone -->
                        <div class="tilt-3d group">
                            <div class="neomorphism p-6 hover-lift transition-all duration-300">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-6">
                                        <h3 class="text-xl font-bold text-gray-900 mb-2">Call Us</h3>
                                        <p class="text-gray-600 mb-1">+255 123 456 789</p>
                                        <p class="text-sm text-gray-500 mb-3">Monday - Friday: 9:00 AM - 5:00 PM</p>
                                        <a href="tel:+255123456789" class="text-primary-600 hover:text-primary-700 font-semibold text-sm flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                            </svg>
                                            Call Now
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Email -->
                        <div class="tilt-3d group">
                            <div class="neomorphism p-6 hover-lift transition-all duration-300">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-6">
                                        <h3 class="text-xl font-bold text-gray-900 mb-2">Email Us</h3>
                                        <p class="text-gray-600 mb-1"><EMAIL></p>
                                        <p class="text-sm text-gray-500 mb-3">We'll respond within 24 hours</p>
                                        <a href="mailto:<EMAIL>" class="text-primary-600 hover:text-primary-700 font-semibold text-sm flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                            </svg>
                                            Send Email
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced Social Media -->
                        <div class="tilt-3d group">
                            <div class="neomorphism p-6 hover-lift transition-all duration-300">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-6">
                                        <h3 class="text-xl font-bold text-gray-900 mb-2">Follow Us</h3>
                                        <p class="text-gray-600 mb-4">Stay connected on social media</p>
                                        <div class="flex space-x-4">
                                            <a href="#" class="social-icon-neumorphism group/social">
                                                <svg class="w-6 h-6 text-blue-600 group-hover/social:text-blue-700 transition-colors" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                                                </svg>
                                            </a>
                                            <a href="#" class="social-icon-neumorphism group/social">
                                                <svg class="w-6 h-6 text-blue-800 group-hover/social:text-blue-900 transition-colors" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                                </svg>
                                            </a>
                                            <a href="#" class="social-icon-neumorphism group/social">
                                                <svg class="w-6 h-6 text-pink-600 group-hover/social:text-pink-700 transition-colors" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
                                                </svg>
                                            </a>
                                            <a href="#" class="social-icon-neumorphism group/social">
                                                <svg class="w-6 h-6 text-red-600 group-hover/social:text-red-700 transition-colors" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Contact Form -->
                <div class="fade-in-right">
                    <div class="glass-card p-8">
                        <h2 class="text-3xl font-bold text-gray-900 mb-8">
                            Send us a <span class="text-gradient">Message</span>
                        </h2>

                        <form action="{{ route('contact.store') }}" method="POST" class="space-y-6" id="contact-form">
                            @csrf

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="form-group">
                                    <label for="name" class="block text-sm font-semibold text-gray-700 mb-2">Full Name *</label>
                                    <input type="text"
                                           id="name"
                                           name="name"
                                           required
                                           class="form-input-glow w-full"
                                           placeholder="Your full name"
                                           onblur="validateField(this)"
                                           oninput="clearError(this)">
                                    <div class="error-message hidden text-red-500 text-sm mt-1"></div>
                                </div>

                                <div class="form-group">
                                    <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">Email Address *</label>
                                    <input type="email"
                                           id="email"
                                           name="email"
                                           required
                                           class="form-input-glow w-full"
                                           placeholder="<EMAIL>"
                                           onblur="validateField(this)"
                                           oninput="clearError(this)">
                                    <div class="error-message hidden text-red-500 text-sm mt-1"></div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="phone" class="block text-sm font-semibold text-gray-700 mb-2">Phone Number</label>
                                <input type="tel"
                                       id="phone"
                                       name="phone"
                                       class="form-input-glow w-full"
                                       placeholder="+255 123 456 789"
                                       onblur="validateField(this)"
                                       oninput="clearError(this)">
                                <div class="error-message hidden text-red-500 text-sm mt-1"></div>
                            </div>

                            <div class="form-group">
                                <label for="subject" class="block text-sm font-semibold text-gray-700 mb-2">Subject *</label>
                                <select id="subject"
                                        name="subject"
                                        required
                                        class="form-input-glow w-full"
                                        onchange="validateField(this)">
                                    <option value="">Select a subject</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="tickets">Tickets & Bookings</option>
                                    <option value="merchandise">Merchandise</option>
                                    <option value="media">Media & Press</option>
                                    <option value="partnerships">Partnerships</option>
                                    <option value="feedback">Feedback</option>
                                    <option value="other">Other</option>
                                </select>
                                <div class="error-message hidden text-red-500 text-sm mt-1"></div>
                            </div>

                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number (Optional)</label>
                            <input type="tel" 
                                   id="phone" 
                                   name="phone"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                   placeholder="+255 123 456 789">
                        </div>

                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                            <select id="subject" 
                                    name="subject" 
                                    required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                <option value="">Select a subject</option>
                                <option value="general">General Inquiry</option>
                                <option value="tickets">Tickets & Bookings</option>
                                <option value="merchandise">Merchandise</option>
                                <option value="media">Media & Press</option>
                                <option value="partnerships">Partnerships & Sponsorship</option>
                                <option value="feedback">Feedback & Suggestions</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                            <textarea id="message" 
                                      name="message" 
                                      rows="6" 
                                      required
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                      placeholder="Tell us how we can help you..."></textarea>
                        </div>

                        <div>
                            <button type="submit" class="w-full btn-primary">
                                Send Message
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Find Us</h2>
                <p class="text-xl text-gray-600">Visit us at Sheikh Amri Abeid Memorial Stadium</p>
            </div>

            <!-- Map Placeholder -->
            <div class="bg-gray-200 rounded-lg h-96 flex items-center justify-center">
                <div class="text-center">
                    <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <p class="text-gray-600">Interactive map will be available soon</p>
                    <p class="text-sm text-gray-500 mt-2">Sheikh Amri Abeid Memorial Stadium, Arusha</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced FAQ Section -->
    <section class="py-16 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 fade-in-up">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">
                    Frequently Asked <span class="text-gradient">Questions</span>
                </h2>
                <p class="text-xl text-gray-600">Quick answers to common questions</p>
            </div>

            <div class="space-y-6">
                <div class="neomorphism p-6 hover-lift transition-all duration-300 fade-in-up">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2 flex items-center">
                        <span class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5h2.5a2.5 2.5 0 0 1 0 5H5m0 0h2.5a2.5 2.5 0 0 1 0 5H5"></path>
                            </svg>
                        </span>
                        How can I purchase tickets for matches?
                    </h3>
                    <p class="text-gray-600 ml-11">You can purchase tickets through our website's ticket section or contact us directly. We'll help you secure the best seats for upcoming matches.</p>
                </div>

                <div class="neomorphism p-6 hover-lift transition-all duration-300 fade-in-up" style="animation-delay: 0.2s;">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2 flex items-center">
                        <span class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                        </span>
                        Where can I buy official merchandise?
                    </h3>
                    <p class="text-gray-600 ml-11">Visit our online store or contact us for information about official Mbuni FC merchandise including jerseys, accessories, and fan gear.</p>
                </div>

                <div class="neomorphism p-6 hover-lift transition-all duration-300 fade-in-up" style="animation-delay: 0.4s;">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2 flex items-center">
                        <span class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </span>
                        How can I become a sponsor or partner?
                    </h3>
                    <p class="text-gray-600 ml-11">We welcome partnerships and sponsorship opportunities. Please contact us with details about your organization and we'll discuss potential collaboration.</p>
                </div>

                <div class="neomorphism p-6 hover-lift transition-all duration-300 fade-in-up" style="animation-delay: 0.6s;">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2 flex items-center">
                        <span class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                            <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </span>
                        Can I visit the stadium for tours?
                    </h3>
                    <p class="text-gray-600 ml-11">Stadium tours may be available on non-match days. Contact us in advance to arrange a visit and learn more about our facilities.</p>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
<script>
    function openMap() {
        const address = "Sheikh Amri Abeid Memorial Stadium, Arusha, Tanzania";
        const encodedAddress = encodeURIComponent(address);
        window.open(`https://www.google.com/maps/search/${encodedAddress}`, '_blank');
    }

    function updateCharCount(textarea) {
        const charCount = textarea.value.length;
        document.getElementById('char-count').textContent = charCount;

        if (charCount > 500) {
            document.getElementById('char-count').classList.add('text-red-500');
        } else {
            document.getElementById('char-count').classList.remove('text-red-500');
        }
    }

    function validateField(field) {
        // Basic validation - can be enhanced
        const value = field.value.trim();
        const errorDiv = field.parentNode.querySelector('.error-message');

        if (field.required && value === '') {
            if (errorDiv) {
                errorDiv.textContent = 'This field is required';
                errorDiv.classList.remove('hidden');
            }
            field.classList.add('border-red-500');
            return false;
        }

        if (errorDiv) {
            errorDiv.classList.add('hidden');
        }
        field.classList.remove('border-red-500');
        return true;
    }

    function clearError(field) {
        const errorDiv = field.parentNode.querySelector('.error-message');
        if (errorDiv) {
            errorDiv.classList.add('hidden');
        }
        field.classList.remove('border-red-500');
    }
</script>
@endpush
