@extends('admin.layouts.app')

@section('title', 'Add Sponsor')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">Add Sponsor</h1>
        <a href="{{ route('admin.sponsors.index') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
            Back to Sponsors
        </a>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg shadow">
        <form action="{{ route('admin.sponsors.store') }}" method="POST" enctype="multipart/form-data" class="p-6 space-y-6">
            @csrf

            <!-- Sponsor Name -->
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Sponsor Name</label>
                <input type="text" 
                       name="name" 
                       id="name" 
                       value="{{ old('name') }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('name') border-red-500 @enderror"
                       placeholder="Enter sponsor name">
                @error('name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Website URL -->
            <div>
                <label for="website_url" class="block text-sm font-medium text-gray-700 mb-2">Website URL (Optional)</label>
                <input type="url" 
                       name="website_url" 
                       id="website_url" 
                       value="{{ old('website_url') }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('website_url') border-red-500 @enderror"
                       placeholder="https://www.example.com">
                <p class="mt-1 text-sm text-gray-500">Enter the sponsor's website URL</p>
                @error('website_url')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Logo Upload -->
            <div>
                <label for="logo" class="block text-sm font-medium text-gray-700 mb-2">Logo</label>
                <input type="file" 
                       name="logo" 
                       id="logo" 
                       accept="image/*"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('logo') border-red-500 @enderror">
                <p class="mt-1 text-sm text-gray-500">Upload sponsor logo (JPEG, PNG, JPG, GIF, SVG). Max size: 2MB</p>
                @error('logo')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Logo Preview -->
            <div id="logo-preview" class="hidden">
                <label class="block text-sm font-medium text-gray-700 mb-2">Logo Preview</label>
                <div class="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                    <img id="preview-image" class="max-w-full max-h-full object-contain" alt="Logo preview">
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.sponsors.index') }}" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </a>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    Add Sponsor
                </button>
            </div>
        </form>
    </div>

    <!-- Guidelines -->
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Logo Guidelines</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="font-medium text-gray-900 mb-2">Recommended Formats</h4>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li>• PNG with transparent background</li>
                    <li>• SVG for scalable vector graphics</li>
                    <li>• High-resolution JPEG (minimum 300x300px)</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium text-gray-900 mb-2">Best Practices</h4>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li>• Use high contrast colors</li>
                    <li>• Ensure logo is readable at small sizes</li>
                    <li>• Square or horizontal orientation works best</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const logoInput = document.getElementById('logo');
    const logoPreview = document.getElementById('logo-preview');
    const previewImage = document.getElementById('preview-image');
    
    logoInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        
        if (file) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                logoPreview.classList.remove('hidden');
            };
            
            reader.readAsDataURL(file);
        } else {
            logoPreview.classList.add('hidden');
        }
    });
});
</script>
@endsection
