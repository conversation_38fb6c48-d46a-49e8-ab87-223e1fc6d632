@extends('admin.layouts.app')

@section('title', 'Sponsors Management')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">Sponsors Management</h1>
        <a href="{{ route('admin.sponsors.create') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
            Add New Sponsor
        </a>
    </div>

    <!-- Stats Card -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Sponsors</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $sponsors->total() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">With Website</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $sponsors->whereNotNull('website_url')->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">With Logo</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $sponsors->whereNotNull('logo')->count() }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Sponsors Grid -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        @if($sponsors->count() > 0)
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-6">
                @foreach($sponsors as $sponsor)
                    <div class="bg-gray-50 rounded-lg p-6 hover:shadow-md transition-shadow">
                        <!-- Logo -->
                        <div class="flex items-center justify-center h-24 mb-4 bg-white rounded-lg">
                            @if($sponsor->logo)
                                <img src="{{ $sponsor->logo_url }}" 
                                     alt="{{ $sponsor->name }}" 
                                     class="max-h-20 max-w-full object-contain">
                            @else
                                <div class="text-gray-400 text-center">
                                    <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    <span class="text-xs">No Logo</span>
                                </div>
                            @endif
                        </div>

                        <!-- Sponsor Info -->
                        <div class="text-center">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $sponsor->name }}</h3>
                            
                            @if($sponsor->website_url)
                                <a href="{{ $sponsor->website_url }}" 
                                   target="_blank" 
                                   class="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 mb-3">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                    </svg>
                                    Visit Website
                                </a>
                            @endif

                            <div class="text-xs text-gray-500 mb-4">
                                Added {{ $sponsor->created_at->format('M d, Y') }}
                            </div>

                            <!-- Actions -->
                            <div class="flex justify-center space-x-2 text-sm">
                                <a href="{{ route('admin.sponsors.show', $sponsor) }}" class="text-blue-600 hover:text-blue-900">View</a>
                                <span class="text-gray-300">|</span>
                                <a href="{{ route('admin.sponsors.edit', $sponsor) }}" class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                <span class="text-gray-300">|</span>
                                <form action="{{ route('admin.sponsors.destroy', $sponsor) }}" method="POST" class="inline" 
                                      onsubmit="return confirm('Are you sure you want to delete this sponsor?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                </form>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            @if($sponsors->hasPages())
                <div class="px-6 py-4 border-t border-gray-200">
                    {{ $sponsors->links() }}
                </div>
            @endif
        @else
            <div class="p-6 text-center text-gray-500">
                No sponsors found. <a href="{{ route('admin.sponsors.create') }}" class="text-blue-600 hover:text-blue-900">Add your first sponsor</a>
            </div>
        @endif
    </div>

    <!-- Info Section -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Sponsor Management Tips</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li>Upload high-quality logos in PNG or SVG format for best results</li>
                        <li>Ensure website URLs are complete and working</li>
                        <li>Sponsors appear on the homepage and throughout the site</li>
                        <li>Consider organizing sponsors by partnership level or category</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
