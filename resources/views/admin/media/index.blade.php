@extends('admin.layouts.app')

@section('title', 'Media Management')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">Media Management</h1>
        <a href="{{ route('admin.media.create') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
            Upload Media
        </a>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Media</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $media->total() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Images</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $media->where('file_type', 'image')->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Videos</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $media->where('file_type', 'video')->count() }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Media Grid -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        @if($media->count() > 0)
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-6">
                @foreach($media as $item)
                    <div class="bg-gray-50 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                        <!-- Media Preview -->
                        <div class="relative aspect-w-16 aspect-h-9 bg-gray-200">
                            @if($item->file_type === 'image')
                                <img src="{{ $item->file_url_full }}" 
                                     alt="{{ $item->title }}" 
                                     class="w-full h-32 object-cover">
                            @else
                                <!-- Video Thumbnail -->
                                <div class="w-full h-32 bg-gray-900 flex items-center justify-center relative">
                                    @if(str_contains($item->file_url, 'youtube') || str_contains($item->file_url, 'youtu.be'))
                                        @php
                                            $videoId = null;
                                            if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/', $item->file_url, $matches)) {
                                                $videoId = $matches[1];
                                            }
                                        @endphp
                                        @if($videoId)
                                            <img src="https://img.youtube.com/vi/{{ $videoId }}/maxresdefault.jpg" 
                                                 alt="{{ $item->title }}" 
                                                 class="w-full h-32 object-cover">
                                        @endif
                                    @endif
                                    
                                    <!-- Play Button Overlay -->
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div class="w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
                                            <svg class="w-6 h-6 text-gray-800 ml-1" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M8 5v14l11-7z"/>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <!-- Media Type Badge -->
                            <div class="absolute top-2 right-2">
                                <span class="px-2 py-1 text-xs font-medium bg-black bg-opacity-70 text-white rounded-full">
                                    {{ $item->file_type === 'image' ? 'Photo' : 'Video' }}
                                </span>
                            </div>
                        </div>

                        <!-- Media Info -->
                        <div class="p-4">
                            <h3 class="text-sm font-semibold text-gray-900 mb-1 line-clamp-2">{{ $item->title }}</h3>
                            @if($item->caption)
                                <p class="text-xs text-gray-600 mb-2 line-clamp-2">{{ $item->caption }}</p>
                            @endif
                            
                            <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                                <span>{{ $item->created_at->format('M d, Y') }}</span>
                                @if($item->uploader)
                                    <span>{{ $item->uploader->name }}</span>
                                @endif
                            </div>

                            <!-- Actions -->
                            <div class="flex justify-between text-xs">
                                <a href="{{ route('admin.media.show', $item) }}" class="text-blue-600 hover:text-blue-900">View</a>
                                <a href="{{ route('admin.media.edit', $item) }}" class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                <form action="{{ route('admin.media.destroy', $item) }}" method="POST" class="inline" 
                                      onsubmit="return confirm('Are you sure you want to delete this media?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                </form>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            @if($media->hasPages())
                <div class="px-6 py-4 border-t border-gray-200">
                    {{ $media->links() }}
                </div>
            @endif
        @else
            <div class="p-6 text-center text-gray-500">
                No media found. <a href="{{ route('admin.media.create') }}" class="text-blue-600 hover:text-blue-900">Upload your first media</a>
            </div>
        @endif
    </div>
</div>
@endsection
