@extends('admin.layouts.app')

@section('title', 'Upload Media')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">Upload Media</h1>
        <a href="{{ route('admin.media.index') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
            Back to Media
        </a>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg shadow">
        <form action="{{ route('admin.media.store') }}" method="POST" enctype="multipart/form-data" class="p-6 space-y-6">
            @csrf

            <!-- Title -->
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                <input type="text" 
                       name="title" 
                       id="title" 
                       value="{{ old('title') }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('title') border-red-500 @enderror"
                       placeholder="Enter media title">
                @error('title')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- File Type -->
            <div>
                <label for="file_type" class="block text-sm font-medium text-gray-700 mb-2">Media Type</label>
                <select name="file_type" 
                        id="file_type" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('file_type') border-red-500 @enderror">
                    <option value="">Select media type</option>
                    <option value="image" {{ old('file_type') === 'image' ? 'selected' : '' }}>Image</option>
                    <option value="video" {{ old('file_type') === 'video' ? 'selected' : '' }}>Video</option>
                </select>
                @error('file_type')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Upload Method -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Upload Method</label>
                <div class="space-y-4">
                    <!-- File Upload -->
                    <div>
                        <label class="flex items-center">
                            <input type="radio" name="upload_method" value="file" class="mr-2" checked>
                            <span class="text-sm text-gray-700">Upload File</span>
                        </label>
                        <div id="file-upload-section" class="mt-2">
                            <input type="file" 
                                   name="file_upload" 
                                   id="file_upload" 
                                   accept="image/*,video/*"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('file_upload') border-red-500 @enderror">
                            <p class="mt-1 text-sm text-gray-500">Upload an image or video file. Max size: 20MB</p>
                            @error('file_upload')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- URL Input -->
                    <div>
                        <label class="flex items-center">
                            <input type="radio" name="upload_method" value="url" class="mr-2">
                            <span class="text-sm text-gray-700">External URL (YouTube, etc.)</span>
                        </label>
                        <div id="url-input-section" class="mt-2" style="display: none;">
                            <input type="url" 
                                   name="file_url" 
                                   id="file_url" 
                                   value="{{ old('file_url') }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('file_url') border-red-500 @enderror"
                                   placeholder="https://www.youtube.com/watch?v=...">
                            <p class="mt-1 text-sm text-gray-500">Enter a YouTube URL or direct link to media file</p>
                            @error('file_url')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Caption -->
            <div>
                <label for="caption" class="block text-sm font-medium text-gray-700 mb-2">Caption (Optional)</label>
                <textarea name="caption" 
                          id="caption" 
                          rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('caption') border-red-500 @enderror"
                          placeholder="Add a caption or description...">{{ old('caption') }}</textarea>
                @error('caption')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.media.index') }}" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </a>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    Upload Media
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadMethodRadios = document.querySelectorAll('input[name="upload_method"]');
    const fileUploadSection = document.getElementById('file-upload-section');
    const urlInputSection = document.getElementById('url-input-section');
    const fileTypeSelect = document.getElementById('file_type');
    const fileUploadInput = document.getElementById('file_upload');
    
    // Toggle upload method sections
    uploadMethodRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'file') {
                fileUploadSection.style.display = 'block';
                urlInputSection.style.display = 'none';
            } else {
                fileUploadSection.style.display = 'none';
                urlInputSection.style.display = 'block';
            }
        });
    });
    
    // Update file input accept attribute based on file type
    fileTypeSelect.addEventListener('change', function() {
        if (this.value === 'image') {
            fileUploadInput.accept = 'image/*';
        } else if (this.value === 'video') {
            fileUploadInput.accept = 'video/*';
        } else {
            fileUploadInput.accept = 'image/*,video/*';
        }
    });
});
</script>
@endsection
