@extends('admin.layouts.app')

@section('title', 'Dashboard')
@section('breadcrumb', 'Admin Panel / Dashboard')

@section('content')
<div class="space-y-8">
    <!-- Welcome Section -->
    <div class="admin-card p-8 border border-white/10">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-3xl font-bold text-white mb-2">Welcome back, {{ auth()->user()->name }}! 👋</h2>
                <p class="text-gray-400">Here's what's happening with Mbuni FC today.</p>
            </div>
            <div class="text-right">
                <p class="text-sm text-gray-400">{{ now()->format('l, F j, Y') }}</p>
                <p class="text-lg font-semibold text-white">{{ now()->format('g:i A') }}</p>
            </div>
        </div>
    </div>

    <!-- Enhanced Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- News Stats -->
        <div class="admin-card p-6 border border-blue-500/20 hover:border-blue-500/40 transition-all duration-300 group">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                    </svg>
                </div>
                <div class="text-right">
                    <div class="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center">
                        <span class="text-blue-400 text-sm font-bold">📰</span>
                    </div>
                </div>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-400 mb-1">Total News</p>
                <p class="text-3xl font-bold text-white mb-2 counter" data-target="{{ $stats['total_news'] ?? 0 }}">0</p>
                <div class="flex items-center">
                    <span class="text-xs text-green-400 bg-green-500/20 px-2 py-1 rounded-full">
                        {{ $stats['published_news'] ?? 0 }} published
                    </span>
                </div>
            </div>
        </div>

        <!-- Players Stats -->
        <div class="admin-card p-6 border border-green-500/20 hover:border-green-500/40 transition-all duration-300 group">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="text-right">
                    <div class="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center">
                        <span class="text-green-400 text-sm font-bold">👥</span>
                    </div>
                </div>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-400 mb-1">Total Players</p>
                <p class="text-3xl font-bold text-white mb-2 counter" data-target="{{ $stats['total_players'] ?? 0 }}">0</p>
                <div class="flex items-center">
                    <span class="text-xs text-blue-400 bg-blue-500/20 px-2 py-1 rounded-full">
                        Squad members
                    </span>
                </div>
            </div>
        </div>

        <!-- Fixtures Stats -->
        <div class="admin-card p-6 border border-yellow-500/20 hover:border-yellow-500/40 transition-all duration-300 group">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="text-right">
                    <div class="w-12 h-12 bg-yellow-500/20 rounded-full flex items-center justify-center">
                        <span class="text-yellow-400 text-sm font-bold">📅</span>
                    </div>
                </div>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-400 mb-1">Upcoming Fixtures</p>
                <p class="text-3xl font-bold text-white mb-2 counter" data-target="{{ $stats['upcoming_fixtures'] ?? 0 }}">0</p>
                <div class="flex items-center">
                    <span class="text-xs text-purple-400 bg-purple-500/20 px-2 py-1 rounded-full">
                        Next matches
                    </span>
                </div>
            </div>
        </div>

        <!-- Media Stats -->
        <div class="admin-card p-6 border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 group">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="text-right">
                    <div class="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center">
                        <span class="text-purple-400 text-sm font-bold">🎬</span>
                    </div>
                </div>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-400 mb-1">Media Files</p>
                <p class="text-3xl font-bold text-white mb-2 counter" data-target="{{ $stats['total_media'] ?? 0 }}">0</p>
                <div class="flex items-center">
                    <span class="text-xs text-orange-400 bg-orange-500/20 px-2 py-1 rounded-full">
                        Images & videos
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Performance Chart -->
        <div class="admin-card p-6 border border-white/10">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-white">Performance Overview</h3>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 bg-red-500/20 text-red-400 rounded-lg text-sm">7D</button>
                    <button class="px-3 py-1 bg-white/10 text-gray-400 rounded-lg text-sm">30D</button>
                    <button class="px-3 py-1 bg-white/10 text-gray-400 rounded-lg text-sm">90D</button>
                </div>
            </div>

            <!-- Simple Bar Chart -->
            <div class="chart-container h-64 flex items-end justify-between space-x-2">
                <div class="flex flex-col items-center">
                    <div class="chart-bar bg-gradient-to-t from-blue-500 to-blue-400 w-8 rounded-t-lg transition-all duration-1000" style="height: 60%; transform: scaleY(0);"></div>
                    <span class="text-xs text-gray-400 mt-2">Mon</span>
                </div>
                <div class="flex flex-col items-center">
                    <div class="chart-bar bg-gradient-to-t from-green-500 to-green-400 w-8 rounded-t-lg transition-all duration-1000" style="height: 80%; transform: scaleY(0);"></div>
                    <span class="text-xs text-gray-400 mt-2">Tue</span>
                </div>
                <div class="flex flex-col items-center">
                    <div class="chart-bar bg-gradient-to-t from-yellow-500 to-yellow-400 w-8 rounded-t-lg transition-all duration-1000" style="height: 45%; transform: scaleY(0);"></div>
                    <span class="text-xs text-gray-400 mt-2">Wed</span>
                </div>
                <div class="flex flex-col items-center">
                    <div class="chart-bar bg-gradient-to-t from-purple-500 to-purple-400 w-8 rounded-t-lg transition-all duration-1000" style="height: 90%; transform: scaleY(0);"></div>
                    <span class="text-xs text-gray-400 mt-2">Thu</span>
                </div>
                <div class="flex flex-col items-center">
                    <div class="chart-bar bg-gradient-to-t from-pink-500 to-pink-400 w-8 rounded-t-lg transition-all duration-1000" style="height: 70%; transform: scaleY(0);"></div>
                    <span class="text-xs text-gray-400 mt-2">Fri</span>
                </div>
                <div class="flex flex-col items-center">
                    <div class="chart-bar bg-gradient-to-t from-indigo-500 to-indigo-400 w-8 rounded-t-lg transition-all duration-1000" style="height: 55%; transform: scaleY(0);"></div>
                    <span class="text-xs text-gray-400 mt-2">Sat</span>
                </div>
                <div class="flex flex-col items-center">
                    <div class="chart-bar bg-gradient-to-t from-red-500 to-red-400 w-8 rounded-t-lg transition-all duration-1000" style="height: 85%; transform: scaleY(0);"></div>
                    <span class="text-xs text-gray-400 mt-2">Sun</span>
                </div>
            </div>
        </div>

        <!-- Activity Feed -->
        <div class="admin-card p-6 border border-white/10">
            <h3 class="text-xl font-bold text-white mb-6">Recent Activity</h3>
            <div class="space-y-4 max-h-64 overflow-y-auto">
                <div class="flex items-center p-3 bg-white/5 rounded-lg">
                    <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <p class="text-white text-sm">New article published</p>
                        <p class="text-gray-400 text-xs">2 minutes ago</p>
                    </div>
                </div>

                <div class="flex items-center p-3 bg-white/5 rounded-lg">
                    <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <p class="text-white text-sm">Player profile updated</p>
                        <p class="text-gray-400 text-xs">15 minutes ago</p>
                    </div>
                </div>

                <div class="flex items-center p-3 bg-white/5 rounded-lg">
                    <div class="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <p class="text-white text-sm">Fixture scheduled</p>
                        <p class="text-gray-400 text-xs">1 hour ago</p>
                    </div>
                </div>

                <div class="flex items-center p-3 bg-white/5 rounded-lg">
                    <div class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <p class="text-white text-sm">Media uploaded</p>
                        <p class="text-gray-400 text-xs">3 hours ago</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
                    <p class="text-2xl font-semibold text-gray-900">{{ $stats['total_merchandise'] }}</p>
                    <p class="text-xs text-red-600">{{ $stats['out_of_stock'] }} out of stock</p>
                </div>
            </div>
        </div>

        <!-- Users Stats -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-teal-100 rounded-lg">
                    <svg class="w-6 h-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Users</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $stats['total_users'] }}</p>
                    <p class="text-xs text-gray-500">{{ $stats['total_fans'] }} fans</p>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div class="space-y-2">
                <a href="{{ route('admin.news.create') }}" class="block w-full text-center bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-sm">
                    Add News
                </a>
                <a href="{{ route('admin.fixtures.create') }}" class="block w-full text-center bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors text-sm">
                    Add Fixture
                </a>
                <a href="{{ route('admin.players.create') }}" class="block w-full text-center bg-yellow-600 text-white py-2 px-4 rounded-md hover:bg-yellow-700 transition-colors text-sm">
                    Add Player
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent News -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Recent News</h3>
            </div>
            <div class="p-6">
                @if($recentNews->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentNews as $news)
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900">{{ Str::limit($news->title, 40) }}</h4>
                                    <p class="text-xs text-gray-500">By {{ $news->author->name }} • {{ $news->created_at->diffForHumans() }}</p>
                                </div>
                                <span class="px-2 py-1 text-xs rounded-full {{ $news->status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                    {{ ucfirst($news->status) }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 text-sm">No news articles yet.</p>
                @endif
            </div>
        </div>

        <!-- Upcoming Fixtures -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Upcoming Fixtures</h3>
            </div>
            <div class="p-6">
                @if($upcomingFixtures->count() > 0)
                    <div class="space-y-4">
                        @foreach($upcomingFixtures as $fixture)
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900">vs {{ $fixture->opponent }}</h4>
                                    <p class="text-xs text-gray-500">{{ $fixture->match_date->format('M d, Y') }} • {{ $fixture->stadium }}</p>
                                </div>
                                <span class="text-xs text-gray-500">
                                    {{ $fixture->match_date->format('H:i') }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 text-sm">No upcoming fixtures.</p>
                @endif
            </div>
        </div>
    </div>


</div>

<script>
// Counter Animation
function animateCounters() {
    const counters = document.querySelectorAll('.counter');

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2000; // 2 seconds
        const increment = target / (duration / 16); // 60fps
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current);
        }, 16);
    });
}

// Chart Animation
function animateCharts() {
    const chartBars = document.querySelectorAll('.chart-bar');
    chartBars.forEach((bar, index) => {
        setTimeout(() => {
            bar.style.transform = 'scaleY(1)';
        }, index * 100);
    });
}

// Initialize animations when page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        animateCounters();
        animateCharts();
    }, 500);
});
</script>
@endsection
