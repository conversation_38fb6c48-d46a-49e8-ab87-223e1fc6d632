@extends('admin.layouts.app')

@section('title', 'Site Settings')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">Site Settings</h1>
    </div>

    <!-- Settings Sections -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        <!-- General Settings -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">General Settings</h3>
                <p class="text-sm text-gray-600">Basic site configuration</p>
            </div>
            <div class="p-6 space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Site Name</label>
                    <input type="text" value="Mbuni FC" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Site Description</label>
                    <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>Official website of Mbuni FC - Arusha's premier football club</textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Contact Email</label>
                    <input type="email" value="<EMAIL>" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Contact Phone</label>
                    <input type="tel" value="+255 123 456 789" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                </div>
            </div>
        </div>

        <!-- Social Media Settings -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Social Media</h3>
                <p class="text-sm text-gray-600">Social media links and profiles</p>
            </div>
            <div class="p-6 space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Facebook</label>
                    <input type="url" placeholder="https://facebook.com/mbunifc" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Twitter</label>
                    <input type="url" placeholder="https://twitter.com/mbunifc" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Instagram</label>
                    <input type="url" placeholder="https://instagram.com/mbunifc" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">YouTube</label>
                    <input type="url" placeholder="https://youtube.com/mbunifc" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                </div>
            </div>
        </div>

        <!-- Club Information -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Club Information</h3>
                <p class="text-sm text-gray-600">Basic club details</p>
            </div>
            <div class="p-6 space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Founded Year</label>
                    <input type="number" value="2020" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Home Stadium</label>
                    <input type="text" value="Arusha Stadium" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Stadium Capacity</label>
                    <input type="number" value="10000" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Club Colors</label>
                    <input type="text" value="Blue and Yellow" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                </div>
            </div>
        </div>

        <!-- SEO Settings -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">SEO Settings</h3>
                <p class="text-sm text-gray-600">Search engine optimization</p>
            </div>
            <div class="p-6 space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Meta Keywords</label>
                    <input type="text" value="Mbuni FC, football, Tanzania, Arusha, soccer" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Google Analytics ID</label>
                    <input type="text" placeholder="GA-XXXXXXXXX-X" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Facebook Pixel ID</label>
                    <input type="text" placeholder="Facebook Pixel ID" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                </div>
            </div>
        </div>

    </div>

    <!-- System Information -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">System Information</h3>
            <p class="text-sm text-gray-600">Current system status and information</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ \App\Models\News::count() }}</div>
                    <div class="text-sm text-gray-600">Total News Articles</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ \App\Models\Player::count() }}</div>
                    <div class="text-sm text-gray-600">Total Players</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-600">{{ \App\Models\Fixture::count() }}</div>
                    <div class="text-sm text-gray-600">Total Fixtures</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">{{ \App\Models\Merchandise::count() }}</div>
                    <div class="text-sm text-gray-600">Total Products</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cache Management -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Cache Management</h3>
            <p class="text-sm text-gray-600">Clear application cache to refresh data</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="clearCache('config')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    Clear Config Cache
                </button>
                <button onclick="clearCache('route')" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                    Clear Route Cache
                </button>
                <button onclick="clearCache('view')" class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors">
                    Clear View Cache
                </button>
            </div>
            <div class="mt-4">
                <button onclick="clearCache('all')" class="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                    Clear All Caches
                </button>
            </div>
        </div>
    </div>

    <!-- Maintenance Mode -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Maintenance Mode</h3>
            <p class="text-sm text-gray-600">Put the site in maintenance mode for updates</p>
        </div>
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-sm font-medium text-gray-900">Site Status</h4>
                    <p class="text-sm text-gray-600">Currently: <span class="text-green-600 font-medium">Online</span></p>
                </div>
                <button class="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors" disabled>
                    Enable Maintenance Mode
                </button>
            </div>
        </div>
    </div>

    <!-- Note -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">Settings Note</h3>
                <div class="mt-2 text-sm text-yellow-700">
                    <p>Settings are currently read-only in this demo. In a full implementation, these would be editable and stored in a settings table or configuration files.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function clearCache(type) {
    // In a real implementation, this would make an AJAX call to clear cache
    alert('Cache clearing functionality would be implemented here for: ' + type);
}
</script>
@endsection
