@extends('admin.layouts.app')

@section('title', 'Create Fixture')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">Create Fixture</h1>
        <a href="{{ route('admin.fixtures.index') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
            Back to Fixtures
        </a>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg shadow">
        <form action="{{ route('admin.fixtures.store') }}" method="POST" class="p-6 space-y-6">
            @csrf

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Opponent -->
                <div>
                    <label for="opponent" class="block text-sm font-medium text-gray-700 mb-2">Opponent</label>
                    <input type="text" 
                           name="opponent" 
                           id="opponent" 
                           value="{{ old('opponent') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('opponent') border-red-500 @enderror"
                           placeholder="Enter opponent team name">
                    @error('opponent')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Competition -->
                <div>
                    <label for="competition" class="block text-sm font-medium text-gray-700 mb-2">Competition</label>
                    <input type="text" 
                           name="competition" 
                           id="competition" 
                           value="{{ old('competition') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('competition') border-red-500 @enderror"
                           placeholder="e.g., Premier League, Cup Final">
                    @error('competition')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Match Date -->
                <div>
                    <label for="match_date" class="block text-sm font-medium text-gray-700 mb-2">Match Date & Time</label>
                    <input type="datetime-local" 
                           name="match_date" 
                           id="match_date" 
                           value="{{ old('match_date') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('match_date') border-red-500 @enderror">
                    @error('match_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Venue -->
                <div>
                    <label for="venue" class="block text-sm font-medium text-gray-700 mb-2">Venue</label>
                    <input type="text" 
                           name="venue" 
                           id="venue" 
                           value="{{ old('venue') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('venue') border-red-500 @enderror"
                           placeholder="Stadium name">
                    @error('venue')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Home or Away -->
                <div>
                    <label for="home_or_away" class="block text-sm font-medium text-gray-700 mb-2">Home or Away</label>
                    <select name="home_or_away" 
                            id="home_or_away" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('home_or_away') border-red-500 @enderror">
                        <option value="">Select venue type</option>
                        <option value="home" {{ old('home_or_away') === 'home' ? 'selected' : '' }}>Home</option>
                        <option value="away" {{ old('home_or_away') === 'away' ? 'selected' : '' }}>Away</option>
                    </select>
                    @error('home_or_away')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Status -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" 
                            id="status" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('status') border-red-500 @enderror">
                        <option value="upcoming" {{ old('status') === 'upcoming' ? 'selected' : '' }}>Upcoming</option>
                        <option value="finished" {{ old('status') === 'finished' ? 'selected' : '' }}>Finished</option>
                        <option value="cancelled" {{ old('status') === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                    </select>
                    @error('status')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Score Section (only show if status is finished) -->
            <div id="score-section" class="grid grid-cols-1 md:grid-cols-2 gap-6" style="display: none;">
                <div>
                    <label for="score_mbuni" class="block text-sm font-medium text-gray-700 mb-2">Mbuni FC Score</label>
                    <input type="number" 
                           name="score_mbuni" 
                           id="score_mbuni" 
                           value="{{ old('score_mbuni') }}"
                           min="0"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('score_mbuni') border-red-500 @enderror"
                           placeholder="0">
                    @error('score_mbuni')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="score_opponent" class="block text-sm font-medium text-gray-700 mb-2">Opponent Score</label>
                    <input type="number" 
                           name="score_opponent" 
                           id="score_opponent" 
                           value="{{ old('score_opponent') }}"
                           min="0"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('score_opponent') border-red-500 @enderror"
                           placeholder="0">
                    @error('score_opponent')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.fixtures.index') }}" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </a>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    Create Fixture
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const statusSelect = document.getElementById('status');
    const scoreSection = document.getElementById('score-section');
    
    function toggleScoreSection() {
        if (statusSelect.value === 'finished') {
            scoreSection.style.display = 'grid';
        } else {
            scoreSection.style.display = 'none';
        }
    }
    
    statusSelect.addEventListener('change', toggleScoreSection);
    
    // Check initial state
    toggleScoreSection();
});
</script>
@endsection
