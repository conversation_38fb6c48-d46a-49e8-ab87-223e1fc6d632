@extends('admin.layouts.app')

@section('title', 'Players Management')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">Players Management</h1>
        <a href="{{ route('admin.players.create') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
            Add New Player
        </a>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Players</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $players->total() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Goalkeepers</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $players->where('position', 'GK')->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Defenders</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $players->where('position', 'DF')->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Midfielders</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $players->where('position', 'MF')->count() }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Players Grid -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Squad</h3>
        </div>
        
        @if($players->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-6">
                @foreach($players as $player)
                    <div class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-center space-x-4">
                            <div class="relative">
                                <img src="{{ $player->photo_url }}" alt="{{ $player->name }}" class="w-16 h-16 rounded-full object-cover">
                                <div class="absolute -bottom-1 -right-1 bg-blue-600 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">
                                    {{ $player->shirt_number }}
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <h4 class="text-sm font-medium text-gray-900 truncate">{{ $player->name }}</h4>
                                <p class="text-sm text-gray-500">{{ $player->position_name }}</p>
                                <p class="text-xs text-gray-400">{{ $player->nationality }}</p>
                            </div>
                        </div>
                        
                        <div class="mt-4 grid grid-cols-2 gap-2 text-xs">
                            <div class="text-center">
                                <div class="font-semibold text-gray-900">{{ $player->stats['goals'] ?? 0 }}</div>
                                <div class="text-gray-500">Goals</div>
                            </div>
                            <div class="text-center">
                                <div class="font-semibold text-gray-900">{{ $player->stats['assists'] ?? 0 }}</div>
                                <div class="text-gray-500">Assists</div>
                            </div>
                        </div>
                        
                        <div class="mt-4 flex justify-between">
                            <a href="{{ route('admin.players.show', $player) }}" class="text-blue-600 hover:text-blue-900 text-sm">View</a>
                            <a href="{{ route('admin.players.edit', $player) }}" class="text-indigo-600 hover:text-indigo-900 text-sm">Edit</a>
                            <form action="{{ route('admin.players.destroy', $player) }}" method="POST" class="inline" 
                                  onsubmit="return confirm('Are you sure you want to delete this player?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="text-red-600 hover:text-red-900 text-sm">Delete</button>
                            </form>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="p-6 text-center text-gray-500">
                No players found. <a href="{{ route('admin.players.create') }}" class="text-blue-600 hover:text-blue-900">Add your first player</a>
            </div>
        @endif

        <!-- Pagination -->
        @if($players->hasPages())
            <div class="px-6 py-4 border-t border-gray-200">
                {{ $players->links() }}
            </div>
        @endif
    </div>

    <!-- Position Summary -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Squad by Position</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                @php
                    $positions = [
                        'GK' => ['name' => 'Goalkeepers', 'color' => 'green'],
                        'DF' => ['name' => 'Defenders', 'color' => 'blue'],
                        'MF' => ['name' => 'Midfielders', 'color' => 'yellow'],
                        'FW' => ['name' => 'Forwards', 'color' => 'red']
                    ];
                @endphp
                
                @foreach($positions as $pos => $details)
                    @php $positionPlayers = $players->where('position', $pos); @endphp
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 mb-2">{{ $details['name'] }}</h4>
                        <p class="text-2xl font-bold text-{{ $details['color'] }}-600 mb-2">{{ $positionPlayers->count() }}</p>
                        @if($positionPlayers->count() > 0)
                            <div class="space-y-1">
                                @foreach($positionPlayers->take(3) as $player)
                                    <div class="text-sm text-gray-600">
                                        #{{ $player->shirt_number }} {{ $player->name }}
                                    </div>
                                @endforeach
                                @if($positionPlayers->count() > 3)
                                    <div class="text-xs text-gray-500">
                                        +{{ $positionPlayers->count() - 3 }} more
                                    </div>
                                @endif
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>
@endsection
