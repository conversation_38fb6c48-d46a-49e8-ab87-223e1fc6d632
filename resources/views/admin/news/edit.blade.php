@extends('admin.layouts.app')

@section('title', 'Edit News Article')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">Edit News Article</h1>
        <div class="space-x-2">
            <a href="{{ route('admin.news.show', $news) }}" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">
                View Article
            </a>
            <a href="{{ route('admin.news.index') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
                Back to News
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg shadow">
        <form action="{{ route('admin.news.update', $news) }}" method="POST" enctype="multipart/form-data" class="p-6 space-y-6">
            @csrf
            @method('PUT')

            <!-- Title -->
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                <input type="text" 
                       name="title" 
                       id="title" 
                       value="{{ old('title', $news->title) }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('title') border-red-500 @enderror"
                       placeholder="Enter article title">
                @error('title')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Category -->
            <div>
                <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select name="category" 
                        id="category" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('category') border-red-500 @enderror">
                    <option value="">Select Category</option>
                    <option value="match_report" {{ old('category', $news->category) === 'match_report' ? 'selected' : '' }}>Match Report</option>
                    <option value="club_news" {{ old('category', $news->category) === 'club_news' ? 'selected' : '' }}>Club News</option>
                    <option value="transfer" {{ old('category', $news->category) === 'transfer' ? 'selected' : '' }}>Transfer</option>
                    <option value="interview" {{ old('category', $news->category) === 'interview' ? 'selected' : '' }}>Interview</option>
                </select>
                @error('category')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Current Image -->
            @if($news->image)
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Current Image</label>
                    <div class="flex items-center space-x-4">
                        <img src="{{ $news->image_url }}" alt="{{ $news->title }}" class="h-20 w-20 object-cover rounded-lg">
                        <div>
                            <p class="text-sm text-gray-600">Current featured image</p>
                            <p class="text-xs text-gray-500">Upload a new image to replace this one</p>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Image -->
            <div>
                <label for="image" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ $news->image ? 'Replace Featured Image' : 'Featured Image' }}
                </label>
                <input type="file" 
                       name="image" 
                       id="image" 
                       accept="image/*"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('image') border-red-500 @enderror">
                <p class="mt-1 text-sm text-gray-500">Upload an image (JPEG, PNG, JPG, GIF). Max size: 2MB</p>
                @error('image')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Body -->
            <div>
                <label for="body" class="block text-sm font-medium text-gray-700 mb-2">Article Content</label>
                <textarea name="body" 
                          id="body" 
                          rows="12"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('body') border-red-500 @enderror"
                          placeholder="Write your article content here...">{{ old('body', $news->body) }}</textarea>
                @error('body')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Status -->
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select name="status" 
                        id="status" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('status') border-red-500 @enderror">
                    <option value="draft" {{ old('status', $news->status) === 'draft' ? 'selected' : '' }}>Draft</option>
                    <option value="published" {{ old('status', $news->status) === 'published' ? 'selected' : '' }}>Published</option>
                </select>
                @error('status')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                @if($news->status === 'published' && $news->published_at)
                    <p class="mt-1 text-sm text-gray-500">Originally published: {{ $news->published_at->format('M d, Y \a\t H:i') }}</p>
                @endif
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.news.index') }}" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </a>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    Update Article
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Simple rich text editor enhancement
document.addEventListener('DOMContentLoaded', function() {
    const textarea = document.getElementById('body');
    
    // Add some basic formatting buttons
    const toolbar = document.createElement('div');
    toolbar.className = 'flex space-x-2 mb-2 p-2 bg-gray-50 border border-gray-300 rounded-t-md';
    
    const boldBtn = document.createElement('button');
    boldBtn.type = 'button';
    boldBtn.innerHTML = '<strong>B</strong>';
    boldBtn.className = 'px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-200';
    boldBtn.onclick = () => wrapText('**', '**');
    
    const italicBtn = document.createElement('button');
    italicBtn.type = 'button';
    italicBtn.innerHTML = '<em>I</em>';
    italicBtn.className = 'px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-200';
    italicBtn.onclick = () => wrapText('*', '*');
    
    toolbar.appendChild(boldBtn);
    toolbar.appendChild(italicBtn);
    
    textarea.parentNode.insertBefore(toolbar, textarea);
    textarea.className = textarea.className.replace('rounded-md', 'rounded-b-md rounded-t-none');
    
    function wrapText(before, after) {
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const text = textarea.value;
        const selectedText = text.substring(start, end);
        
        textarea.value = text.substring(0, start) + before + selectedText + after + text.substring(end);
        textarea.focus();
        textarea.setSelectionRange(start + before.length, end + before.length);
    }
});
</script>
@endsection
