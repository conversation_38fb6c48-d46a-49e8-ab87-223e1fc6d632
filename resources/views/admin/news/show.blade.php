@extends('admin.layouts.app')

@section('title', 'View News Article')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900">View News Article</h1>
        <div class="space-x-2">
            <a href="{{ route('admin.news.edit', $news) }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                Edit Article
            </a>
            <a href="{{ route('admin.news.index') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
                Back to News
            </a>
        </div>
    </div>

    <!-- Article Details -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <!-- Article Header -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-4">
                    <span class="px-3 py-1 text-sm font-medium rounded-full 
                        {{ $news->status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                        {{ ucfirst($news->status) }}
                    </span>
                    <span class="px-3 py-1 text-sm font-medium rounded-full bg-blue-100 text-blue-800">
                        {{ $news->category_name }}
                    </span>
                </div>
                <div class="text-sm text-gray-500">
                    Created: {{ $news->created_at->format('M d, Y \a\t H:i') }}
                </div>
            </div>

            <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ $news->title }}</h1>

            <div class="flex items-center text-sm text-gray-600">
                <div class="flex items-center mr-6">
                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-2">
                        {{ substr($news->author->name, 0, 1) }}
                    </div>
                    <span>By {{ $news->author->name }}</span>
                </div>
                @if($news->published_at)
                    <div class="mr-6">
                        Published: {{ $news->published_at->format('M d, Y \a\t H:i') }}
                    </div>
                @endif
                <div>
                    Slug: <code class="bg-gray-100 px-2 py-1 rounded text-xs">{{ $news->slug }}</code>
                </div>
            </div>
        </div>

        <!-- Featured Image -->
        @if($news->image)
            <div class="p-6 border-b border-gray-200">
                <img src="{{ $news->image_url }}" alt="{{ $news->title }}" class="w-full max-w-2xl mx-auto rounded-lg shadow-md">
            </div>
        @endif

        <!-- Article Content -->
        <div class="p-6">
            <div class="prose max-w-none">
                {!! nl2br(e($news->body)) !!}
            </div>
        </div>

        <!-- Article Footer -->
        <div class="p-6 border-t border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-600">
                    <p>Last updated: {{ $news->updated_at->format('M d, Y \a\t H:i') }}</p>
                    @if($news->updated_at != $news->created_at)
                        <p>{{ $news->updated_at->diffForHumans() }}</p>
                    @endif
                </div>
                <div class="flex space-x-2">
                    @if($news->status === 'published')
                        <a href="{{ route('news.show', $news) }}" target="_blank" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                            </svg>
                            View on Site
                        </a>
                    @endif
                    <form action="{{ route('admin.news.destroy', $news) }}" method="POST" class="inline" 
                          onsubmit="return confirm('Are you sure you want to delete this article? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete Article
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="{{ route('admin.news.create') }}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="p-2 bg-blue-100 rounded-lg mr-4">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-900">Create New Article</h4>
                    <p class="text-sm text-gray-500">Write another news article</p>
                </div>
            </a>

            <a href="{{ route('admin.news.index') }}?status=draft" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="p-2 bg-yellow-100 rounded-lg mr-4">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-900">View Drafts</h4>
                    <p class="text-sm text-gray-500">See unpublished articles</p>
                </div>
            </a>

            <a href="{{ route('admin.news.index') }}?status=published" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="p-2 bg-green-100 rounded-lg mr-4">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-900">Published Articles</h4>
                    <p class="text-sm text-gray-500">View live articles</p>
                </div>
            </a>
        </div>
    </div>
</div>
@endsection
