import './bootstrap';

import Alpine from 'alpinejs';

window.Alpine = Alpine;

Alpine.start();

// Modern Mbuni FC Interactions
document.addEventListener('DOMContentLoaded', function() {

    // Logo Intro Animation
    const logoIntro = document.getElementById('logo-intro');
    if (logoIntro) {
        setTimeout(() => {
            logoIntro.style.display = 'none';
        }, 3000);
    }

    // Hero Section Cursor Ripple Effect
    const heroSection = document.getElementById('hero-section');
    if (heroSection) {
        heroSection.addEventListener('click', function(e) {
            const ripple = document.createElement('div');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple-effect');
            ripple.style.position = 'absolute';
            ripple.style.pointerEvents = 'none';
            ripple.style.zIndex = '5';

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    }

    // Cursor Ripple Effect
    function createRipple(event) {
        const button = event.currentTarget;
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple-effect');

        button.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    // Add ripple effect to buttons
    document.querySelectorAll('.btn-primary, .btn-secondary, .btn-outline, .ripple').forEach(button => {
        button.addEventListener('click', createRipple);
    });

    // Intersection Observer for Animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.fade-in-up, .fade-in-left, .fade-in-right, .scale-in').forEach(el => {
        observer.observe(el);
    });

    // Smooth Scrolling for Navigation
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Enhanced Mobile Menu
    const mobileMenuButton = document.querySelector('.mobile-menu-button');
    const mobileMenu = document.querySelector('.mobile-menu');

    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
            mobileMenu.classList.toggle('animate-slide-in-up');
        });
    }

    // Lazy Loading Images with Fade Effect
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.add('fade-in');
                imageObserver.unobserve(img);
            }
        });
    });

    document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
    });

    // Carousel Functionality
    function initCarousel(container) {
        const carousel = container.querySelector('.carousel-container');
        const prevBtn = container.querySelector('.carousel-prev');
        const nextBtn = container.querySelector('.carousel-next');
        const items = carousel.querySelectorAll('.carousel-item');

        if (!carousel || items.length === 0) return;

        let currentIndex = 0;
        const itemWidth = items[0].offsetWidth + 16; // Including gap

        function updateCarousel() {
            carousel.scrollTo({
                left: currentIndex * itemWidth,
                behavior: 'smooth'
            });
        }

        if (prevBtn) {
            prevBtn.addEventListener('click', () => {
                currentIndex = Math.max(0, currentIndex - 1);
                updateCarousel();
            });
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', () => {
                currentIndex = Math.min(items.length - 1, currentIndex + 1);
                updateCarousel();
            });
        }

        // Auto-scroll for hero carousel
        if (container.classList.contains('auto-scroll')) {
            setInterval(() => {
                currentIndex = (currentIndex + 1) % items.length;
                updateCarousel();
            }, 5000);
        }
    }

    // Initialize all carousels
    document.querySelectorAll('.carousel-wrapper').forEach(initCarousel);

    // Shopping Cart Animation
    function animateCartAdd(button) {
        button.classList.add('bounce-cart');
        const cartIcon = document.querySelector('.cart-icon');
        if (cartIcon) {
            cartIcon.classList.add('animate-heartbeat');
            setTimeout(() => {
                cartIcon.classList.remove('animate-heartbeat');
            }, 1500);
        }

        setTimeout(() => {
            button.classList.remove('bounce-cart');
        }, 600);
    }

    // Add to cart buttons
    document.querySelectorAll('.add-to-cart').forEach(button => {
        button.addEventListener('click', function() {
            animateCartAdd(this);
        });
    });

    // Confetti Animation
    function createConfetti() {
        const confettiContainer = document.createElement('div');
        confettiContainer.classList.add('confetti');
        document.body.appendChild(confettiContainer);

        const colors = ['#dc2626', '#fbbf24', '#ffffff', '#10b981'];

        for (let i = 0; i < 50; i++) {
            const confettiPiece = document.createElement('div');
            confettiPiece.classList.add('confetti-piece');
            confettiPiece.style.left = Math.random() * 100 + '%';
            confettiPiece.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            confettiPiece.style.animationDelay = Math.random() * 3 + 's';
            confettiContainer.appendChild(confettiPiece);
        }

        setTimeout(() => {
            confettiContainer.remove();
        }, 3000);
    }

    // Trigger confetti on success actions
    document.querySelectorAll('.success-action').forEach(button => {
        button.addEventListener('click', createConfetti);
    });

    // Modal Functionality
    function openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
            modal.classList.add('animate-fade-in');
            document.body.style.overflow = 'hidden';
        }
    }

    function closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
            modal.classList.remove('animate-fade-in');
            document.body.style.overflow = 'auto';
        }
    }

    // Modal triggers
    document.querySelectorAll('[data-modal-open]').forEach(trigger => {
        trigger.addEventListener('click', function() {
            const modalId = this.getAttribute('data-modal-open');
            openModal(modalId);
        });
    });

    document.querySelectorAll('[data-modal-close]').forEach(trigger => {
        trigger.addEventListener('click', function() {
            const modalId = this.getAttribute('data-modal-close');
            closeModal(modalId);
        });
    });

    // Close modal on backdrop click
    document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
        backdrop.addEventListener('click', function(e) {
            if (e.target === this) {
                const modalId = this.id;
                closeModal(modalId);
            }
        });
    });

    // 3D Tilt Effect
    function initTiltEffect() {
        document.querySelectorAll('.tilt-3d').forEach(element => {
            element.addEventListener('mousemove', function(e) {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                const centerX = rect.width / 2;
                const centerY = rect.height / 2;

                const rotateX = (y - centerY) / centerY * -10;
                const rotateY = (x - centerX) / centerX * 10;

                this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
            });

            element.addEventListener('mouseleave', function() {
                this.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg)';
            });
        });
    }

    initTiltEffect();

    // Scoreboard Animation
    function animateScore(element, finalScore) {
        let currentScore = 0;
        const increment = finalScore / 30; // 30 frames

        function updateScore() {
            currentScore += increment;
            if (currentScore >= finalScore) {
                element.textContent = finalScore;
                element.classList.add('animate-count-up');
            } else {
                element.textContent = Math.floor(currentScore);
                requestAnimationFrame(updateScore);
            }
        }

        updateScore();
    }

    // Initialize score animations when visible
    const scoreObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const scoreElement = entry.target;
                const finalScore = parseInt(scoreElement.getAttribute('data-score'));
                animateScore(scoreElement, finalScore);
                scoreObserver.unobserve(scoreElement);
            }
        });
    });

    document.querySelectorAll('.animate-score').forEach(el => {
        scoreObserver.observe(el);
    });

    // Form Validation with Animations
    function validateForm(form) {
        const inputs = form.querySelectorAll('input[required], textarea[required]');
        let isValid = true;

        inputs.forEach(input => {
            const errorElement = input.parentNode.querySelector('.error-message');

            if (!input.value.trim()) {
                isValid = false;
                input.classList.add('border-red-500', 'animate-shake');
                if (errorElement) {
                    errorElement.classList.remove('hidden');
                    errorElement.classList.add('animate-slide-in-up');
                }

                setTimeout(() => {
                    input.classList.remove('animate-shake');
                }, 600);
            } else {
                input.classList.remove('border-red-500');
                if (errorElement) {
                    errorElement.classList.add('hidden');
                    errorElement.classList.remove('animate-slide-in-up');
                }
            }
        });

        return isValid;
    }

    // Enhanced form submissions
    document.querySelectorAll('form.validate').forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
    });

    // Progress Bar Animation
    function animateProgressBar(progressBar, targetWidth) {
        const fill = progressBar.querySelector('.progress-fill');
        if (fill) {
            setTimeout(() => {
                fill.style.width = targetWidth + '%';
            }, 100);
        }
    }

    // Initialize progress bars
    document.querySelectorAll('.progress-bar[data-progress]').forEach(bar => {
        const progress = parseInt(bar.getAttribute('data-progress'));
        animateProgressBar(bar, progress);
    });

    // Toggle Switch Functionality
    document.querySelectorAll('.toggle-switch').forEach(toggle => {
        toggle.addEventListener('click', function() {
            this.classList.toggle('active');
            const input = this.querySelector('input[type="checkbox"]');
            if (input) {
                input.checked = this.classList.contains('active');
            }
        });
    });

    // Lightbox Gallery
    function initLightbox() {
        const lightbox = document.createElement('div');
        lightbox.className = 'fixed inset-0 bg-black bg-opacity-90 z-50 hidden flex items-center justify-center p-4';
        lightbox.innerHTML = `
            <div class="relative max-w-4xl max-h-full">
                <img class="max-w-full max-h-full object-contain" src="" alt="">
                <button class="absolute top-4 right-4 text-white text-2xl hover:text-gray-300">&times;</button>
                <button class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white text-2xl hover:text-gray-300">&#8249;</button>
                <button class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white text-2xl hover:text-gray-300">&#8250;</button>
            </div>
        `;
        document.body.appendChild(lightbox);

        let currentImages = [];
        let currentIndex = 0;

        function showImage(index) {
            const img = lightbox.querySelector('img');
            img.src = currentImages[index];
            currentIndex = index;
        }

        // Gallery triggers
        document.querySelectorAll('.gallery-item').forEach((item, index) => {
            item.addEventListener('click', function() {
                currentImages = Array.from(document.querySelectorAll('.gallery-item img')).map(img => img.src);
                showImage(index);
                lightbox.classList.remove('hidden');
            });
        });

        // Navigation
        lightbox.querySelector('button:nth-child(3)').addEventListener('click', () => {
            currentIndex = (currentIndex - 1 + currentImages.length) % currentImages.length;
            showImage(currentIndex);
        });

        lightbox.querySelector('button:nth-child(4)').addEventListener('click', () => {
            currentIndex = (currentIndex + 1) % currentImages.length;
            showImage(currentIndex);
        });

        // Close
        lightbox.querySelector('button:nth-child(2)').addEventListener('click', () => {
            lightbox.classList.add('hidden');
        });

        lightbox.addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.add('hidden');
            }
        });
    }

    initLightbox();
});
