<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\FixtureController;
use App\Http\Controllers\PlayerController;
use App\Http\Controllers\MerchandiseController;
use App\Http\Controllers\MediaGalleryController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\Admin\DashboardController;
use Illuminate\Support\Facades\Route;

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');

// News Routes
Route::get('/news', [NewsController::class, 'index'])->name('news.index');
Route::get('/news/{news:slug}', [NewsController::class, 'show'])->name('news.show');

// Fixtures Routes
Route::get('/fixtures', [FixtureController::class, 'index'])->name('fixtures.index');

// Squad Routes
Route::get('/squad', [PlayerController::class, 'index'])->name('squad.index');
Route::get('/players/{player}', [PlayerController::class, 'show'])->name('players.show');

// Club Pages
Route::get('/club', function () {
    return view('club.index');
})->name('club.index');

Route::get('/club/history', function () {
    return view('club.history');
})->name('club.history');

// Tickets (Maintenance)
Route::get('/tickets', function () {
    return view('tickets.maintenance');
})->name('tickets.index');

// Store Routes
Route::get('/store', [MerchandiseController::class, 'index'])->name('store.index');
Route::get('/store/{merchandise}', [MerchandiseController::class, 'show'])->name('store.show');

// Media Gallery
Route::get('/media', [MediaGalleryController::class, 'index'])->name('media.index');

// Contact
Route::get('/contact', function () {
    return view('contact.index');
})->name('contact.index');

Route::post('/contact', function () {
    // Handle contact form submission
    return back()->with('success', 'Message sent successfully!');
})->name('contact.store');

// Cart Routes (for authenticated users)
Route::middleware('auth')->group(function () {
    Route::get('/cart', [CartController::class, 'index'])->name('cart.index');
    Route::post('/cart/add/{merchandise}', [CartController::class, 'add'])->name('cart.add');
    Route::patch('/cart/{merchandise}', [CartController::class, 'update'])->name('cart.update');
    Route::delete('/cart/{merchandise}', [CartController::class, 'remove'])->name('cart.remove');
    Route::post('/cart/checkout', [CartController::class, 'checkout'])->name('cart.checkout');
});

// User Dashboard
Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Profile Routes
Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Admin Routes (for admin and editor roles)
Route::middleware(['auth', 'verified', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    // News Management
    Route::resource('news', App\Http\Controllers\Admin\NewsController::class);

    // Player Management
    Route::resource('players', App\Http\Controllers\Admin\PlayerController::class);

    // Fixture Management
    Route::resource('fixtures', App\Http\Controllers\Admin\FixtureController::class);

    // Merchandise Management
    Route::resource('merchandise', App\Http\Controllers\Admin\MerchandiseController::class);

    // Media Gallery Management
    Route::resource('media', App\Http\Controllers\Admin\MediaGalleryController::class);

    // Sponsor Management
    Route::resource('sponsors', App\Http\Controllers\Admin\SponsorController::class);

    Route::get('/settings', function () {
        return view('admin.settings.index');
    })->name('settings.index');
});

require __DIR__.'/auth.php';
