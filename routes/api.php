<?php

use App\Http\Controllers\Api\V1\NewsController;
use App\Http\Controllers\Api\V1\FixtureController;
use App\Http\Controllers\Api\V1\PlayerController;
use App\Http\Controllers\Api\V1\MerchandiseController;
use App\Http\Controllers\Api\V1\MediaGalleryController;
use App\Http\Controllers\Api\V1\AuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// API v1 routes
Route::prefix('v1')->group(function () {
    
    // Authentication routes
    Route::post('/auth/login', [AuthController::class, 'login']);
    
    // Protected routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/auth/logout', [AuthController::class, 'logout']);
        Route::get('/auth/user', [AuthController::class, 'user']);
    });

    // Public API routes
    Route::apiResource('news', NewsController::class)->only(['index', 'show']);
    Route::apiResource('fixtures', FixtureController::class)->only(['index', 'show']);
    Route::apiResource('players', PlayerController::class)->only(['index', 'show']);
    Route::apiResource('store', MerchandiseController::class)->only(['index', 'show']);
    Route::apiResource('gallery', MediaGalleryController::class)->only(['index', 'show']);

    // Additional endpoints
    Route::get('/news/category/{category}', [NewsController::class, 'byCategory']);
    Route::get('/fixtures/upcoming', [FixtureController::class, 'upcoming']);
    Route::get('/fixtures/results', [FixtureController::class, 'results']);
    Route::get('/players/position/{position}', [PlayerController::class, 'byPosition']);
    Route::get('/store/category/{category}', [MerchandiseController::class, 'byCategory']);
    Route::get('/gallery/images', [MediaGalleryController::class, 'images']);
    Route::get('/gallery/videos', [MediaGalleryController::class, 'videos']);
});

// Fallback route for API
Route::fallback(function () {
    return response()->json([
        'message' => 'API endpoint not found',
        'status' => 404
    ], 404);
});
