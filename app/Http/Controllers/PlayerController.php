<?php

namespace App\Http\Controllers;

use App\Models\Player;
use Illuminate\Http\Request;

class PlayerController extends Controller
{
    public function index(Request $request)
    {
        $query = Player::query();

        // Filter by position if provided
        if ($request->has('position') && $request->position) {
            $query->where('position', $request->position);
        }

        // Order by position hierarchy and shirt number
        $players = $query->orderByRaw("
            CASE position
                WHEN 'GK' THEN 1
                WHEN 'DF' THEN 2
                WHEN 'MF' THEN 3
                WHEN 'FW' THEN 4
            END
        ")->orderBy('shirt_number')->get();

        // Group players by position for better display
        $playersByPosition = $players->groupBy('position');

        $positions = [
            'GK' => 'Goalkeepers',
            'DF' => 'Defenders',
            'MF' => 'Midfielders',
            'FW' => 'Forwards'
        ];

        return view('squad.index', compact('playersByPosition', 'positions'));
    }

    public function show(Player $player)
    {
        return view('players.show', compact('player'));
    }
}
