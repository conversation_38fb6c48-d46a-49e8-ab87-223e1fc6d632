<?php

namespace App\Http\Controllers;

use App\Models\Merchandise;
use Illuminate\Http\Request;

class MerchandiseController extends Controller
{
    public function index(Request $request)
    {
        $query = Merchandise::query();

        // Filter by category if provided
        if ($request->has('category') && $request->category) {
            $query->where('category', $request->category);
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by availability
        if ($request->has('in_stock') && $request->in_stock) {
            $query->inStock();
        }

        // Sort options
        $sortBy = $request->get('sort', 'name');
        $sortOrder = $request->get('order', 'asc');

        switch ($sortBy) {
            case 'price':
                $query->orderBy('price', $sortOrder);
                break;
            case 'newest':
                $query->latest();
                break;
            default:
                $query->orderBy('name', $sortOrder);
        }

        $merchandise = $query->paginate(12);

        // Get unique categories for filter
        $categories = Merchandise::distinct()->pluck('category')->filter();

        return view('store.index', compact('merchandise', 'categories'));
    }

    public function show(Merchandise $merchandise)
    {
        // Get related products from same category
        $relatedProducts = Merchandise::where('category', $merchandise->category)
            ->where('id', '!=', $merchandise->id)
            ->inStock()
            ->take(4)
            ->get();

        return view('store.show', compact('merchandise', 'relatedProducts'));
    }
}
