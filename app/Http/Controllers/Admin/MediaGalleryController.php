<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\MediaGallery;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class MediaGalleryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $media = MediaGallery::with('uploader')->latest()->paginate(15);
        return view('admin.media.index', compact('media'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.media.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'file_type' => 'required|in:image,video',
            'file_url' => 'required|string',
            'caption' => 'nullable|string',
            'file_upload' => 'nullable|file|mimes:jpeg,png,jpg,gif,mp4,avi,mov|max:20480',
        ]);

        $data = $request->all();
        $data['uploaded_by'] = auth()->id();

        // Handle file upload
        if ($request->hasFile('file_upload')) {
            $folder = $request->file_type === 'image' ? 'media/images' : 'media/videos';
            $data['file_url'] = $request->file('file_upload')->store($folder, 'public');
        }

        MediaGallery::create($data);

        return redirect()->route('admin.media.index')
            ->with('success', 'Media uploaded successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(MediaGallery $medium)
    {
        return view('admin.media.show', compact('medium'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(MediaGallery $medium)
    {
        return view('admin.media.edit', compact('medium'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, MediaGallery $medium)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'file_type' => 'required|in:image,video',
            'file_url' => 'required|string',
            'caption' => 'nullable|string',
        ]);

        $medium->update($request->all());

        return redirect()->route('admin.media.index')
            ->with('success', 'Media updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MediaGallery $medium)
    {
        // Delete file if it's stored locally
        if (!filter_var($medium->file_url, FILTER_VALIDATE_URL)) {
            Storage::disk('public')->delete($medium->file_url);
        }

        $medium->delete();

        return redirect()->route('admin.media.index')
            ->with('success', 'Media deleted successfully.');
    }
}
