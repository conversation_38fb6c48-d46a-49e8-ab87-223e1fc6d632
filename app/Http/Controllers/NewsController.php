<?php

namespace App\Http\Controllers;

use App\Models\News;
use Illuminate\Http\Request;

class NewsController extends Controller
{
    public function index(Request $request)
    {
        $query = News::published()->with('author');

        // Filter by category if provided
        if ($request->has('category') && $request->category) {
            $query->where('category', $request->category);
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('body', 'like', '%' . $request->search . '%');
            });
        }

        $news = $query->latest('published_at')->paginate(12);

        // Get categories for filter
        $categories = [
            'match_report' => 'Match Reports',
            'club_news' => 'Club News',
            'transfer' => 'Transfers',
            'interview' => 'Interviews'
        ];

        return view('news.index', compact('news', 'categories'));
    }

    public function show(News $news)
    {
        // Only show published articles
        if ($news->status !== 'published') {
            abort(404);
        }

        // Load the author relationship
        $news->load('author');

        // Get related articles (same category, excluding current)
        $relatedNews = News::published()
            ->where('category', $news->category)
            ->where('id', '!=', $news->id)
            ->latest('published_at')
            ->take(3)
            ->get();

        return view('news.show', compact('news', 'relatedNews'));
    }
}
