<?php

namespace App\Http\Controllers;

use App\Models\Fixture;
use App\Models\Ticket;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TicketController extends Controller
{
    /**
     * Display the tickets page
     */
    public function index()
    {
        // Get upcoming fixtures that have tickets available
        $upcomingFixtures = Fixture::upcoming()
            ->with('tickets')
            ->get()
            ->map(function ($fixture) {
                $fixture->available_tickets = $this->getAvailableTickets($fixture);
                return $fixture;
            });

        // Get user's tickets if authenticated
        $userTickets = Auth::check() 
            ? Ticket::where('user_id', Auth::id())
                ->with(['fixture'])
                ->orderBy('created_at', 'desc')
                ->get()
            : collect();

        // Ticket types and pricing
        $ticketTypes = [
            'regular' => [
                'name' => 'Regular',
                'price' => 15000,
                'description' => 'Standard seating with great view of the pitch',
                'features' => ['Stadium entry', 'Standard seating', 'Match program']
            ],
            'vip' => [
                'name' => 'VIP',
                'price' => 35000,
                'description' => 'Premium experience with exclusive benefits',
                'features' => ['Premium seating', 'Complimentary refreshments', 'VIP parking', 'Match program', 'Meet & greet opportunity']
            ],
            'season' => [
                'name' => 'Season Pass',
                'price' => 300000,
                'description' => 'Access to all home matches for the entire season',
                'features' => ['All home matches', 'Priority booking', 'Exclusive merchandise', '10% store discount', 'Season holder events']
            ]
        ];

        return view('tickets.index', compact('upcomingFixtures', 'userTickets', 'ticketTypes'));
    }

    /**
     * Show ticket booking form for a specific fixture
     */
    public function show(Fixture $fixture)
    {
        $availableTickets = $this->getAvailableTickets($fixture);
        
        $ticketTypes = [
            'regular' => [
                'name' => 'Regular',
                'price' => 15000,
                'available' => $availableTickets['regular'] ?? 0,
                'description' => 'Standard seating with great view of the pitch'
            ],
            'vip' => [
                'name' => 'VIP', 
                'price' => 35000,
                'available' => $availableTickets['vip'] ?? 0,
                'description' => 'Premium experience with exclusive benefits'
            ]
        ];

        return view('tickets.show', compact('fixture', 'ticketTypes'));
    }

    /**
     * Handle ticket booking request
     */
    public function book(Request $request, Fixture $fixture)
    {
        $request->validate([
            'ticket_type' => 'required|in:regular,vip',
            'quantity' => 'required|integer|min:1|max:4',
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
        ]);

        // Check availability
        $availableTickets = $this->getAvailableTickets($fixture);
        $requestedType = $request->ticket_type;
        
        if (($availableTickets[$requestedType] ?? 0) < $request->quantity) {
            return back()->withErrors(['quantity' => 'Not enough tickets available for the selected type.']);
        }

        // Calculate total price
        $prices = [
            'regular' => 15000,
            'vip' => 35000
        ];
        
        $totalPrice = $prices[$requestedType] * $request->quantity;

        // For now, we'll just show a success message
        // In a real implementation, you'd integrate with a payment gateway
        
        return back()->with([
            'success' => 'Ticket booking request received! We will contact you shortly to complete the booking.',
            'booking_details' => [
                'fixture' => $fixture->opponent . ' vs Mbuni FC',
                'date' => $fixture->match_date->format('M d, Y H:i'),
                'venue' => $fixture->venue,
                'ticket_type' => ucfirst($requestedType),
                'quantity' => $request->quantity,
                'total_price' => $totalPrice,
                'contact_name' => $request->name,
                'contact_email' => $request->email,
                'contact_phone' => $request->phone,
            ]
        ]);
    }

    /**
     * Get available tickets for a fixture
     */
    private function getAvailableTickets(Fixture $fixture): array
    {
        // Mock data - in real implementation, you'd calculate based on venue capacity and sold tickets
        $capacity = [
            'regular' => 500,
            'vip' => 50
        ];

        $sold = [
            'regular' => $fixture->tickets()->where('ticket_type', 'regular')->where('status', '!=', 'cancelled')->count(),
            'vip' => $fixture->tickets()->where('ticket_type', 'vip')->where('status', '!=', 'cancelled')->count()
        ];

        return [
            'regular' => max(0, $capacity['regular'] - $sold['regular']),
            'vip' => max(0, $capacity['vip'] - $sold['vip'])
        ];
    }
}
