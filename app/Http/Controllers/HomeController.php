<?php

namespace App\Http\Controllers;

use App\Models\News;
use App\Models\Fixture;
use App\Models\Sponsor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class HomeController extends Controller
{
    public function index()
    {
        // Cache home page data for 15 minutes
        $latestNews = Cache::remember('home.latest_news', 900, function () {
            return News::published()
                ->with('author')
                ->latest('published_at')
                ->take(3)
                ->get();
        });

        $nextFixture = Cache::remember('home.next_fixture', 900, function () {
            return Fixture::upcoming()->first();
        });

        $recentResults = Cache::remember('home.recent_results', 900, function () {
            return Fixture::finished()->take(3)->get();
        });

        $sponsors = Cache::remember('home.sponsors', 3600, function () {
            return Sponsor::all();
        });

        return view('home', compact(
            'latestNews',
            'nextFixture',
            'recentResults',
            'sponsors'
        ));
    }
}
