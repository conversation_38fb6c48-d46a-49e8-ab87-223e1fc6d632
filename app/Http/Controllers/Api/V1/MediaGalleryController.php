<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\MediaGallery;
use Illuminate\Http\Request;

class MediaGalleryController extends Controller
{
    /**
     * Display a listing of media
     */
    public function index(Request $request)
    {
        $query = MediaGallery::with('uploader');

        // Filter by file type if provided
        if ($request->has('type')) {
            $query->where('file_type', $request->type);
        }

        // Search functionality
        if ($request->has('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('caption', 'like', '%' . $request->search . '%');
            });
        }

        // Pagination
        $perPage = min($request->get('per_page', 20), 50);
        $media = $query->latest()->paginate($perPage);

        return response()->json([
            'data' => $media->items(),
            'meta' => [
                'current_page' => $media->currentPage(),
                'last_page' => $media->lastPage(),
                'per_page' => $media->perPage(),
                'total' => $media->total(),
            ],
            'links' => [
                'first' => $media->url(1),
                'last' => $media->url($media->lastPage()),
                'prev' => $media->previousPageUrl(),
                'next' => $media->nextPageUrl(),
            ]
        ]);
    }

    /**
     * Display the specified media
     */
    public function show(MediaGallery $gallery)
    {
        $gallery->load('uploader');

        return response()->json([
            'data' => $gallery
        ]);
    }

    /**
     * Get images only
     */
    public function images(Request $request)
    {
        $query = MediaGallery::images()->with('uploader');

        $perPage = min($request->get('per_page', 20), 50);
        $images = $query->latest()->paginate($perPage);

        return response()->json([
            'data' => $images->items(),
            'meta' => [
                'current_page' => $images->currentPage(),
                'last_page' => $images->lastPage(),
                'per_page' => $images->perPage(),
                'total' => $images->total(),
                'type' => 'images',
            ]
        ]);
    }

    /**
     * Get videos only
     */
    public function videos(Request $request)
    {
        $query = MediaGallery::videos()->with('uploader');

        $perPage = min($request->get('per_page', 20), 50);
        $videos = $query->latest()->paginate($perPage);

        return response()->json([
            'data' => $videos->items(),
            'meta' => [
                'current_page' => $videos->currentPage(),
                'last_page' => $videos->lastPage(),
                'per_page' => $videos->perPage(),
                'total' => $videos->total(),
                'type' => 'videos',
            ]
        ]);
    }
}
