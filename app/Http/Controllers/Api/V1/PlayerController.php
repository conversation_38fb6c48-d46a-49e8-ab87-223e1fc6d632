<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Player;
use Illuminate\Http\Request;

class PlayerController extends Controller
{
    /**
     * Display a listing of players
     */
    public function index(Request $request)
    {
        $query = Player::query();

        // Filter by position if provided
        if ($request->has('position')) {
            $query->where('position', $request->position);
        }

        // Search functionality
        if ($request->has('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('nationality', 'like', '%' . $request->search . '%');
            });
        }

        // Pagination
        $perPage = min($request->get('per_page', 25), 50);
        $players = $query->orderBy('shirt_number')->paginate($perPage);

        return response()->json([
            'data' => $players->items(),
            'meta' => [
                'current_page' => $players->currentPage(),
                'last_page' => $players->lastPage(),
                'per_page' => $players->perPage(),
                'total' => $players->total(),
            ],
            'links' => [
                'first' => $players->url(1),
                'last' => $players->url($players->lastPage()),
                'prev' => $players->previousPageUrl(),
                'next' => $players->nextPageUrl(),
            ]
        ]);
    }

    /**
     * Display the specified player
     */
    public function show(Player $player)
    {
        return response()->json([
            'data' => $player
        ]);
    }

    /**
     * Get players by position
     */
    public function byPosition(Request $request, $position)
    {
        $validPositions = ['GK', 'DF', 'MF', 'FW'];
        
        if (!in_array(strtoupper($position), $validPositions)) {
            return response()->json([
                'message' => 'Invalid position'
            ], 400);
        }

        $query = Player::where('position', strtoupper($position));

        $perPage = min($request->get('per_page', 25), 50);
        $players = $query->orderBy('shirt_number')->paginate($perPage);

        return response()->json([
            'data' => $players->items(),
            'meta' => [
                'current_page' => $players->currentPage(),
                'last_page' => $players->lastPage(),
                'per_page' => $players->perPage(),
                'total' => $players->total(),
                'position' => strtoupper($position),
            ]
        ]);
    }
}
