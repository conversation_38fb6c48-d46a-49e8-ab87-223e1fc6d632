<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Merchandise;
use Illuminate\Http\Request;

class MerchandiseController extends Controller
{
    /**
     * Display a listing of merchandise
     */
    public function index(Request $request)
    {
        $query = Merchandise::query();

        // Filter by category if provided
        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        // Filter by availability
        if ($request->has('in_stock') && $request->in_stock) {
            $query->inStock();
        }

        // Search functionality
        if ($request->has('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        // Price range filter
        if ($request->has('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }

        if ($request->has('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        
        if (in_array($sortBy, ['name', 'price', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        }

        // Pagination
        $perPage = min($request->get('per_page', 20), 50);
        $merchandise = $query->paginate($perPage);

        return response()->json([
            'data' => $merchandise->items(),
            'meta' => [
                'current_page' => $merchandise->currentPage(),
                'last_page' => $merchandise->lastPage(),
                'per_page' => $merchandise->perPage(),
                'total' => $merchandise->total(),
            ],
            'links' => [
                'first' => $merchandise->url(1),
                'last' => $merchandise->url($merchandise->lastPage()),
                'prev' => $merchandise->previousPageUrl(),
                'next' => $merchandise->nextPageUrl(),
            ]
        ]);
    }

    /**
     * Display the specified merchandise
     */
    public function show(Merchandise $merchandise)
    {
        return response()->json([
            'data' => $merchandise
        ]);
    }

    /**
     * Get merchandise by category
     */
    public function byCategory(Request $request, $category)
    {
        $query = Merchandise::where('category', $category);

        $perPage = min($request->get('per_page', 20), 50);
        $merchandise = $query->orderBy('name')->paginate($perPage);

        return response()->json([
            'data' => $merchandise->items(),
            'meta' => [
                'current_page' => $merchandise->currentPage(),
                'last_page' => $merchandise->lastPage(),
                'per_page' => $merchandise->perPage(),
                'total' => $merchandise->total(),
                'category' => $category,
            ]
        ]);
    }
}
