<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Fixture;
use Illuminate\Http\Request;

class FixtureController extends Controller
{
    /**
     * Display a listing of fixtures
     */
    public function index(Request $request)
    {
        $query = Fixture::query();

        // Filter by status if provided
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->has('from_date')) {
            $query->where('match_date', '>=', $request->from_date);
        }

        if ($request->has('to_date')) {
            $query->where('match_date', '<=', $request->to_date);
        }

        // Filter by home/away
        if ($request->has('venue_type')) {
            $query->where('home_or_away', $request->venue_type);
        }

        // Pagination
        $perPage = min($request->get('per_page', 15), 50);
        $fixtures = $query->orderBy('match_date', 'desc')->paginate($perPage);

        return response()->json([
            'data' => $fixtures->items(),
            'meta' => [
                'current_page' => $fixtures->currentPage(),
                'last_page' => $fixtures->lastPage(),
                'per_page' => $fixtures->perPage(),
                'total' => $fixtures->total(),
            ],
            'links' => [
                'first' => $fixtures->url(1),
                'last' => $fixtures->url($fixtures->lastPage()),
                'prev' => $fixtures->previousPageUrl(),
                'next' => $fixtures->nextPageUrl(),
            ]
        ]);
    }

    /**
     * Display the specified fixture
     */
    public function show(Fixture $fixture)
    {
        return response()->json([
            'data' => $fixture
        ]);
    }

    /**
     * Get upcoming fixtures
     */
    public function upcoming(Request $request)
    {
        $perPage = min($request->get('per_page', 10), 50);
        $fixtures = Fixture::upcoming()->paginate($perPage);

        return response()->json([
            'data' => $fixtures->items(),
            'meta' => [
                'current_page' => $fixtures->currentPage(),
                'last_page' => $fixtures->lastPage(),
                'per_page' => $fixtures->perPage(),
                'total' => $fixtures->total(),
            ]
        ]);
    }

    /**
     * Get finished fixtures (results)
     */
    public function results(Request $request)
    {
        $perPage = min($request->get('per_page', 10), 50);
        $fixtures = Fixture::finished()->paginate($perPage);

        return response()->json([
            'data' => $fixtures->items(),
            'meta' => [
                'current_page' => $fixtures->currentPage(),
                'last_page' => $fixtures->lastPage(),
                'per_page' => $fixtures->perPage(),
                'total' => $fixtures->total(),
            ]
        ]);
    }
}
