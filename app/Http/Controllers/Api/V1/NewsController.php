<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\News;
use Illuminate\Http\Request;

class NewsController extends Controller
{
    /**
     * Display a listing of news articles
     */
    public function index(Request $request)
    {
        $query = News::published()->with('author');

        // Filter by category if provided
        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        // Search functionality
        if ($request->has('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('body', 'like', '%' . $request->search . '%');
            });
        }

        // Pagination
        $perPage = min($request->get('per_page', 15), 50); // Max 50 items per page
        $news = $query->latest('published_at')->paginate($perPage);

        return response()->json([
            'data' => $news->items(),
            'meta' => [
                'current_page' => $news->currentPage(),
                'last_page' => $news->lastPage(),
                'per_page' => $news->perPage(),
                'total' => $news->total(),
                'from' => $news->firstItem(),
                'to' => $news->lastItem(),
            ],
            'links' => [
                'first' => $news->url(1),
                'last' => $news->url($news->lastPage()),
                'prev' => $news->previousPageUrl(),
                'next' => $news->nextPageUrl(),
            ]
        ]);
    }

    /**
     * Display the specified news article
     */
    public function show(News $news)
    {
        // Only show published articles
        if ($news->status !== 'published') {
            return response()->json([
                'message' => 'Article not found'
            ], 404);
        }

        $news->load('author');

        return response()->json([
            'data' => $news
        ]);
    }

    /**
     * Get news by category
     */
    public function byCategory(Request $request, $category)
    {
        $validCategories = ['match_report', 'club_news', 'transfer', 'interview'];
        
        if (!in_array($category, $validCategories)) {
            return response()->json([
                'message' => 'Invalid category'
            ], 400);
        }

        $query = News::published()
            ->with('author')
            ->where('category', $category);

        $perPage = min($request->get('per_page', 15), 50);
        $news = $query->latest('published_at')->paginate($perPage);

        return response()->json([
            'data' => $news->items(),
            'meta' => [
                'current_page' => $news->currentPage(),
                'last_page' => $news->lastPage(),
                'per_page' => $news->perPage(),
                'total' => $news->total(),
                'category' => $category,
            ],
            'links' => [
                'first' => $news->url(1),
                'last' => $news->url($news->lastPage()),
                'prev' => $news->previousPageUrl(),
                'next' => $news->nextPageUrl(),
            ]
        ]);
    }
}
