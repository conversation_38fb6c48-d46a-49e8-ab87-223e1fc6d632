<?php

namespace App\Http\Controllers;

use App\Models\Fixture;
use Illuminate\Http\Request;

class FixtureController extends Controller
{
    public function index(Request $request)
    {
        $query = Fixture::query();

        // Filter by competition if provided
        if ($request->has('competition') && $request->competition) {
            $query->where('competition', $request->competition);
        }

        // Filter by status
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        // Default ordering: upcoming first, then by date
        $fixtures = $query->orderByRaw("
            CASE
                WHEN status = 'upcoming' THEN 1
                WHEN status = 'live' THEN 2
                ELSE 3
            END
        ")->orderBy('match_date', 'desc')->paginate(20);

        // Get unique competitions for filter
        $competitions = Fixture::distinct()->pluck('competition')->filter();

        // Separate upcoming and finished fixtures for better display
        $upcomingFixtures = Fixture::upcoming()->take(5)->get();
        $recentResults = Fixture::finished()->take(5)->get();

        return view('fixtures.index', compact(
            'fixtures',
            'competitions',
            'upcomingFixtures',
            'recentResults'
        ));
    }
}
