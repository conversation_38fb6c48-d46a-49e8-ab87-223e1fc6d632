<?php

namespace App\Http\Controllers;

use App\Models\Merchandise;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CartController extends Controller
{
    public function index()
    {
        $cart = session()->get('cart', []);
        $cartItems = [];
        $total = 0;

        foreach ($cart as $id => $details) {
            $merchandise = Merchandise::find($id);
            if ($merchandise) {
                $cartItems[] = [
                    'merchandise' => $merchandise,
                    'quantity' => $details['quantity'],
                    'subtotal' => $merchandise->price * $details['quantity']
                ];
                $total += $merchandise->price * $details['quantity'];
            }
        }

        return view('cart.index', compact('cartItems', 'total'));
    }

    public function add(Request $request, Merchandise $merchandise)
    {
        $request->validate([
            'quantity' => 'required|integer|min:1|max:10'
        ]);

        if (!$merchandise->is_in_stock) {
            return back()->with('error', 'This item is out of stock.');
        }

        if ($merchandise->stock_quantity < $request->quantity) {
            return back()->with('error', 'Not enough stock available.');
        }

        $cart = session()->get('cart', []);
        $quantity = $request->quantity;

        if (isset($cart[$merchandise->id])) {
            $newQuantity = $cart[$merchandise->id]['quantity'] + $quantity;
            if ($newQuantity > $merchandise->stock_quantity) {
                return back()->with('error', 'Not enough stock available.');
            }
            $cart[$merchandise->id]['quantity'] = $newQuantity;
        } else {
            $cart[$merchandise->id] = [
                'quantity' => $quantity,
                'price' => $merchandise->price
            ];
        }

        session()->put('cart', $cart);

        return back()->with('success', 'Item added to cart successfully!');
    }

    public function update(Request $request, Merchandise $merchandise)
    {
        $request->validate([
            'quantity' => 'required|integer|min:1|max:10'
        ]);

        $cart = session()->get('cart', []);

        if (isset($cart[$merchandise->id])) {
            if ($merchandise->stock_quantity < $request->quantity) {
                return back()->with('error', 'Not enough stock available.');
            }

            $cart[$merchandise->id]['quantity'] = $request->quantity;
            session()->put('cart', $cart);
        }

        return back()->with('success', 'Cart updated successfully!');
    }

    public function remove(Merchandise $merchandise)
    {
        $cart = session()->get('cart', []);

        if (isset($cart[$merchandise->id])) {
            unset($cart[$merchandise->id]);
            session()->put('cart', $cart);
        }

        return back()->with('success', 'Item removed from cart!');
    }

    public function checkout(Request $request)
    {
        $cart = session()->get('cart', []);

        if (empty($cart)) {
            return redirect()->route('store.index')->with('error', 'Your cart is empty.');
        }

        DB::beginTransaction();

        try {
            // Calculate total
            $total = 0;
            foreach ($cart as $id => $details) {
                $merchandise = Merchandise::find($id);
                if ($merchandise) {
                    $total += $merchandise->price * $details['quantity'];
                }
            }

            // Create order
            $order = Order::create([
                'user_id' => auth()->id(),
                'total_price' => $total,
                'status' => 'pending'
            ]);

            // Create order items and update stock
            foreach ($cart as $id => $details) {
                $merchandise = Merchandise::find($id);
                if ($merchandise) {
                    // Check stock availability
                    if ($merchandise->stock_quantity < $details['quantity']) {
                        throw new \Exception("Not enough stock for {$merchandise->name}");
                    }

                    // Create order item
                    OrderItem::create([
                        'order_id' => $order->id,
                        'merchandise_id' => $id,
                        'quantity' => $details['quantity'],
                        'price' => $merchandise->price
                    ]);

                    // Update stock
                    $merchandise->decreaseStock($details['quantity']);
                }
            }

            // Clear cart
            session()->forget('cart');

            DB::commit();

            return redirect()->route('dashboard')->with('success', 'Order placed successfully! Order ID: ' . $order->id);

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Error processing order: ' . $e->getMessage());
        }
    }
}
