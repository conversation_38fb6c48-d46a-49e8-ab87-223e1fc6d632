<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Fixture extends Model
{
    use HasFactory;

    protected $fillable = [
        'opponent',
        'competition',
        'match_date',
        'venue',
        'home_or_away',
        'score_mbuni',
        'score_opponent',
        'status',
    ];

    protected function casts(): array
    {
        return [
            'match_date' => 'datetime',
        ];
    }

    /**
     * Get tickets for this fixture
     */
    public function tickets(): HasMany
    {
        return $this->hasMany(Ticket::class);
    }

    /**
     * Scope for upcoming fixtures
     */
    public function scopeUpcoming($query)
    {
        return $query->where('status', 'upcoming')
                    ->where('match_date', '>', now())
                    ->orderBy('match_date');
    }

    /**
     * Scope for finished fixtures
     */
    public function scopeFinished($query)
    {
        return $query->where('status', 'finished')
                    ->orderBy('match_date', 'desc');
    }

    /**
     * Scope for home fixtures
     */
    public function scopeHome($query)
    {
        return $query->where('home_or_away', 'home');
    }

    /**
     * Scope for away fixtures
     */
    public function scopeAway($query)
    {
        return $query->where('home_or_away', 'away');
    }

    /**
     * Get formatted match result
     */
    public function getResultAttribute(): string
    {
        if ($this->status !== 'finished' || is_null($this->score_mbuni) || is_null($this->score_opponent)) {
            return 'TBD';
        }

        return $this->home_or_away === 'home'
            ? "{$this->score_mbuni} - {$this->score_opponent}"
            : "{$this->score_opponent} - {$this->score_mbuni}";
    }

    /**
     * Check if match is today
     */
    public function getIsToday(): bool
    {
        return $this->match_date->isToday();
    }

    /**
     * Get match status badge color
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'upcoming' => 'blue',
            'live' => 'red',
            'finished' => 'green',
            default => 'gray',
        };
    }
}
