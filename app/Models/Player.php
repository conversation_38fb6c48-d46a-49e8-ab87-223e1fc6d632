<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Player extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'position',
        'shirt_number',
        'photo',
        'nationality',
        'bio',
        'stats',
    ];

    protected function casts(): array
    {
        return [
            'stats' => 'array',
        ];
    }

    /**
     * Get the player's photo URL
     */
    public function getPhotoUrlAttribute(): string
    {
        return $this->photo ? asset('storage/' . $this->photo) : asset('images/default-player.png');
    }

    /**
     * Get formatted position name
     */
    public function getPositionNameAttribute(): string
    {
        return match($this->position) {
            'GK' => 'Goalkeeper',
            'DF' => 'Defender',
            'MF' => 'Midfielder',
            'FW' => 'Forward',
            default => $this->position,
        };
    }
}
