<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Sponsor extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'logo',
        'website_url',
    ];

    /**
     * Get the sponsor logo URL
     */
    public function getLogoUrlAttribute(): string
    {
        return $this->logo ? asset('storage/' . $this->logo) : asset('images/default-sponsor.png');
    }

    /**
     * Get formatted website URL
     */
    public function getFormattedWebsiteUrlAttribute(): ?string
    {
        if (!$this->website_url) {
            return null;
        }

        // Add https:// if no protocol is specified
        if (!preg_match('/^https?:\/\//', $this->website_url)) {
            return 'https://' . $this->website_url;
        }

        return $this->website_url;
    }

    /**
     * Check if sponsor has a website
     */
    public function getHasWebsiteAttribute(): bool
    {
        return !empty($this->website_url);
    }
}
