<?php

namespace App\Helpers;

class SeoHelper
{
    /**
     * Generate meta tags for news articles
     */
    public static function newsMetaTags($news)
    {
        return [
            'title' => $news->title . ' - Mbuni FC',
            'description' => $news->excerpt,
            'keywords' => 'Mbuni FC, football, Tanzania, ' . $news->category_name,
            'og:title' => $news->title,
            'og:description' => $news->excerpt,
            'og:image' => $news->image_url,
            'og:url' => route('news.show', $news),
            'og:type' => 'article',
            'article:author' => $news->author->name,
            'article:published_time' => $news->published_at?->toISOString(),
            'article:section' => $news->category_name,
            'twitter:card' => 'summary_large_image',
            'twitter:title' => $news->title,
            'twitter:description' => $news->excerpt,
            'twitter:image' => $news->image_url,
        ];
    }

    /**
     * Generate meta tags for player profiles
     */
    public static function playerMetaTags($player)
    {
        return [
            'title' => $player->name . ' - Player Profile - Mbuni FC',
            'description' => $player->bio ?: "Learn more about {$player->name}, {$player->position_name} for Mbuni FC.",
            'keywords' => "Mbuni FC, {$player->name}, {$player->position_name}, football player, Tanzania",
            'og:title' => $player->name . ' - Mbuni FC',
            'og:description' => $player->bio ?: "Learn more about {$player->name}, {$player->position_name} for Mbuni FC.",
            'og:image' => $player->photo_url,
            'og:url' => route('players.show', $player),
            'og:type' => 'profile',
            'twitter:card' => 'summary',
            'twitter:title' => $player->name . ' - Mbuni FC',
            'twitter:description' => $player->bio ?: "Learn more about {$player->name}, {$player->position_name} for Mbuni FC.",
            'twitter:image' => $player->photo_url,
        ];
    }

    /**
     * Generate default meta tags
     */
    public static function defaultMetaTags()
    {
        return [
            'title' => 'Mbuni FC - Official Website',
            'description' => 'Official website of Mbuni FC - Tanzanian football club. Get the latest news, fixtures, results, and player information.',
            'keywords' => 'Mbuni FC, football, Tanzania, soccer, sports, fixtures, results, news',
            'og:title' => 'Mbuni FC - Official Website',
            'og:description' => 'Official website of Mbuni FC - Tanzanian football club.',
            'og:image' => asset('images/mbuni-logo.svg'),
            'og:url' => route('home'),
            'og:type' => 'website',
            'twitter:card' => 'summary',
            'twitter:title' => 'Mbuni FC - Official Website',
            'twitter:description' => 'Official website of Mbuni FC - Tanzanian football club.',
            'twitter:image' => asset('images/mbuni-logo.svg'),
        ];
    }

    /**
     * Render meta tags as HTML
     */
    public static function renderMetaTags($metaTags)
    {
        $html = '';
        
        foreach ($metaTags as $name => $content) {
            if (str_starts_with($name, 'og:') || str_starts_with($name, 'twitter:') || str_starts_with($name, 'article:')) {
                $html .= '<meta property="' . $name . '" content="' . htmlspecialchars($content) . '">' . "\n";
            } else {
                $html .= '<meta name="' . $name . '" content="' . htmlspecialchars($content) . '">' . "\n";
            }
        }
        
        return $html;
    }
}
